[core]
	repositoryformatversion = 0
	filemode = true
	bare = false
	logallrefupdates = true
	ignorecase = true
	precomposeunicode = true
[remote "origin"]
	url = **************:treebo-noss/cataloging-service.git
	fetch = +refs/heads/*:refs/remotes/origin/*
[branch "main"]
	remote = origin
	merge = refs/heads/main
[branch "feat/PROM-18630-upgrade-python-version"]
	remote = origin
	merge = refs/heads/feat/PROM-18630-upgrade-python-version
[branch "feature/PROM-18504-department-base-changes"]
	remote = origin
	merge = refs/heads/feature/PROM-18504-department-base-changes
[branch "feat/PROM-18694-onboarding"]
	remote = origin
	merge = refs/heads/feat/PROM-18694-onboarding
