from flask_admin.form import FileU<PERSON><PERSON><PERSON>ield
from wtforms.fields import <PERSON><PERSON>ield

from cataloging_service.domain import service_provider


class BaseFileUploadField(FileUploadField):
    def populate_obj(self, obj, name):
        field = getattr(obj, name, None)
        if field:
            if self._should_delete:
                self._delete_file(field)
                setattr(obj, name, None)
                return

        if self._is_uploaded_file(self.data):
            if field:
                self._delete_file(field)

            filename = self.generate_name(obj, self.data)
            obj.file_name = filename
            self._pre_save(obj, self.data)
            self.data.filename = filename

            setattr(obj, name, filename)

    def _pre_save(self, model_object, file_data):
        pass


class GoogleDriveFileUploadField(BaseFileUploadField):
    def _pre_save(self, model_object, file_data):
        service_provider.google_drive_file_upload_service.save_property_file(model_object, self.data)


class TrimmedStringField(StringField):
    def process_formdata(self, valuelist):
        if valuelist:
            self.data = valuelist[0].strip()
        else:
            self.data = ''
