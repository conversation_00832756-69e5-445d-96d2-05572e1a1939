# Property SKU API
"""
Property SKU CRUD operations.

This module handles all SKU-related operations for properties:
- Create SKU in a property
- Update SKU (PUT - complete replacement)
- Partially update SKU (PATCH - partial update)
"""

from flask import Blueprint

from cataloging_service.domain.entities.properties import PropertySkuEntity
from cataloging_service.domain.services.property import PropertySkuService
from cataloging_service.schemas.template_schemas import (
    PropertySkuCreateSchema,
    PropertySkuUpdateSchema,
    PropertySkuResponseSchema,
)

# Import simplified API response handler
from cataloging_service.common.api_responses import ApiResponse, api_endpoint
from cataloging_service.common.request_decorators import parse_request
from object_registry import inject

bp = Blueprint("property_skus", __name__)


@bp.route("/<string:property_id>/skus", methods=["POST"])
@inject(sku_service=PropertySkuService)
@parse_request(body_schema=PropertySkuCreateSchema, inject_path_params=True)
@api_endpoint
def create_property_sku(data: PropertySkuCreateSchema, property_id: str, sku_service):
    """
    Create a SKU directly in a property (without template)
    ---
    tags:
      - Property Entities
    parameters:
      - name: property_id
        in: path
        schema:
          type: string
        required: true
        description: Property ID
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/PropertySkuCreateSchema'
    responses:
      201:
        description: SKU created successfully
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PropertySkuResponseSchema'
      400:
        description: Validation error
    """
    # Create domain entity using automatic conversion
    sku_entity = PropertySkuEntity.model_validate(data)

    # Use injected service to create SKU
    created_sku = sku_service.create_property_sku(sku_entity)

    # Convert to response schema using automatic conversion
    response_data = PropertySkuResponseSchema.model_validate(created_sku)

    return ApiResponse.created(response_data)


@bp.route("/<string:property_id>/skus/<int:sku_id>", methods=["PUT"])
@inject(sku_service=PropertySkuService)
@parse_request(body_schema=PropertySkuCreateSchema, inject_path_params=True)
@api_endpoint
def replace_property_sku(
    data: PropertySkuCreateSchema, property_id: str, sku_id: int, sku_service
):
    """
    Replace a property SKU (complete resource replacement)
    ---
    tags:
      - Property Entities
    parameters:
      - name: property_id
        in: path
        schema:
          type: string
        required: true
        description: Property ID
      - name: sku_id
        in: path
        schema:
          type: integer
        required: true
        description: SKU ID
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/PropertySkuCreateSchema'
    responses:
      200:
        description: Replaced property SKU
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PropertySkuResponseSchema'
      400:
        description: Validation error
      404:
        description: SKU not found
    """
    # Check if SKU exists
    existing_sku = sku_service.get_property_sku_by_id(sku_id)
    if not existing_sku or existing_sku.property_id != property_id:
        return ApiResponse.not_found("Property SKU", sku_id)

    # PUT = Complete replacement - use all fields from request
    replacement_data = data.model_dump()
    replacement_data["id"] = sku_id
    replacement_data["property_id"] = property_id

    # Create replacement entity (complete replacement)
    replacement_entity = PropertySkuEntity.model_validate(replacement_data)

    # Use service to update SKU
    updated_sku = sku_service.update_property_sku(replacement_entity)

    # Convert to response schema using automatic conversion
    response_data = PropertySkuResponseSchema.model_validate(updated_sku)

    return ApiResponse.success(response_data)


@bp.route("/<string:property_id>/skus/<int:sku_id>", methods=["PATCH"])
@inject(sku_service=PropertySkuService)
@parse_request(body_schema=PropertySkuUpdateSchema, inject_path_params=True)
@api_endpoint
def patch_property_sku(
    data: PropertySkuUpdateSchema, property_id: str, sku_id: int, sku_service
):
    """
    Partially update a property SKU (only provided fields)
    ---
    tags:
      - Property Entities
    parameters:
      - name: property_id
        in: path
        schema:
          type: string
        required: true
        description: Property ID
      - name: sku_id
        in: path
        schema:
          type: integer
        required: true
        description: SKU ID
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/PropertySkuUpdateSchema'
    responses:
      200:
        description: Partially updated property SKU
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PropertySkuResponseSchema'
      400:
        description: Validation error
      404:
        description: SKU not found
    """
    # Get existing SKU
    existing_sku = sku_service.get_property_sku_by_id(sku_id)
    if not existing_sku or existing_sku.property_id != property_id:
        return ApiResponse.not_found("Property SKU", sku_id)

    # PATCH = Partial update - merge only provided fields
    # Use exclude_unset=True to allow setting fields to None while excluding unprovided fields
    update_data = data.model_dump(exclude_unset=True)
    update_data["id"] = sku_id
    update_data["property_id"] = property_id

    # Create updated entity by merging existing with provided fields
    updated_entity = PropertySkuEntity.model_validate(
        {**existing_sku.model_dump(), **update_data}
    )

    # Use service to update SKU
    updated_sku = sku_service.update_property_sku(updated_entity)

    # Convert to response schema using automatic conversion
    response_data = PropertySkuResponseSchema.model_validate(updated_sku)

    return ApiResponse.success(response_data)
