# Department Template API
"""
Department Template CRUD operations.

This module handles all department template-related operations:
- Create department template
- List department templates with filters
- Update department template (PUT - complete replacement)
- Partially update department template (PATCH - partial update)
"""

from flask import Blueprint

from cataloging_service.schemas.template_schemas import (
    DepartmentTemplateCreateSchema,
    DepartmentTemplateUpdateSchema,
    DepartmentTemplateResponseSchema,
)
from cataloging_service.schemas.filter_schemas import DepartmentTemplateFilterSchema
from cataloging_service.domain.entities.templates.department_template import (
    DepartmentTemplateEntity,
)
from cataloging_service.domain.services.template import (
    DepartmentTemplateService,
    TemplateFilter,
)

# Import simplified API response handler
from cataloging_service.common.api_responses import ApiResponse, api_endpoint
from cataloging_service.common.request_decorators import parse_body, parse_query
from object_registry import inject

bp = Blueprint("template_departments", __name__)


@bp.route("/departments", methods=["POST"])
@inject(department_service=DepartmentTemplateService)
@parse_body(DepartmentTemplateCreateSchema)
@api_endpoint
def create_department_template(
    data: DepartmentTemplateCreateSchema, department_service
):
    """
    Create a new department template
    ---
    tags:
      - Template Management
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/DepartmentTemplateCreateSchema'
    responses:
      201:
        description: Department template created successfully
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DepartmentTemplateResponseSchema'
      400:
        description: Validation error
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ErrorResponse'
      500:
        description: Internal server error
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ErrorResponse'
    """
    # Create domain entity using automatic conversion
    department_entity = DepartmentTemplateEntity.model_validate(data)

    # Use service to create department template
    created_department = department_service.create_department_template(
        department_entity
    )

    # Convert to response schema using automatic conversion
    response_data = DepartmentTemplateResponseSchema.model_validate(created_department)

    return ApiResponse.created(response_data)


@bp.route("/departments", methods=["GET"])
@inject(department_service=DepartmentTemplateService)
@parse_query(DepartmentTemplateFilterSchema)
@api_endpoint
def get_department_templates(
    filters: DepartmentTemplateFilterSchema, department_service
):
    """
    Get department templates with optional filters
    ---
    tags:
      - Template Management
    parameters:
      - name: brand_id
        in: query
        schema:
          type: integer
        required: false
        description: Optional Brand ID to filter templates
      - name: parent_code
        in: query
        schema:
          type: string
        required: false
        description: Optional parent department code to filter templates
      - name: is_active
        in: query
        schema:
          type: boolean
          default: true
        required: false
        description: Whether to return only active templates
      - name: auto_create_on_property_launch
        in: query
        schema:
          type: boolean
        required: false
        description: Filter by auto-create flag
    responses:
      200:
        description: List of department templates
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/DepartmentTemplateResponseSchema'
      500:
        description: Internal server error
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ErrorResponse'
    """
    # Convert Pydantic schema to TemplateFilter
    template_filter = TemplateFilter.from_schema(filters)

    # Get department templates using service with filters
    department_templates = department_service.get_department_templates(template_filter)

    # Convert to response schemas using automatic conversion
    response_templates = [
        DepartmentTemplateResponseSchema.model_validate(template)
        for template in department_templates
    ]

    return ApiResponse.success(response_templates)


@bp.route("/departments/<int:template_id>", methods=["PUT"])
@inject(department_service=DepartmentTemplateService)
@parse_body(DepartmentTemplateCreateSchema)
@api_endpoint
def replace_department_template(
    data: DepartmentTemplateCreateSchema, template_id: int, department_service
):
    """
    Replace a department template (complete resource replacement)
    ---
    tags:
      - Template Management
    parameters:
      - name: template_id
        in: path
        schema:
          type: integer
        required: true
        description: Template ID
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/DepartmentTemplateCreateSchema'
    responses:
      200:
        description: Replaced department template
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DepartmentTemplateResponseSchema'
      400:
        description: Validation error
      404:
        description: Template not found
    """
    # Check if template exists
    existing_department = department_service.get_department_template_by_id(template_id)
    if not existing_department:
        return ApiResponse.not_found("Department Template", template_id)

    # PUT = Complete replacement - use all fields from request
    replacement_data = data.model_dump()
    replacement_data["id"] = template_id

    # Create replacement entity (complete replacement)
    replacement_entity = DepartmentTemplateEntity.model_validate(replacement_data)

    # Use service to update department template
    updated_department = department_service.update_department_template(
        replacement_entity
    )

    # Convert to response schema using automatic conversion
    response_data = DepartmentTemplateResponseSchema.model_validate(updated_department)

    return ApiResponse.success(response_data)


@bp.route("/departments/<int:template_id>", methods=["PATCH"])
@inject(department_service=DepartmentTemplateService)
@parse_body(DepartmentTemplateUpdateSchema)
@api_endpoint
def patch_department_template(
    data: DepartmentTemplateUpdateSchema, template_id: int, department_service
):
    """
    Partially update a department template (only provided fields)
    ---
    tags:
      - Template Management
    parameters:
      - name: template_id
        in: path
        schema:
          type: integer
        required: true
        description: Template ID
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/DepartmentTemplateUpdateSchema'
    responses:
      200:
        description: Partially updated department template
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DepartmentTemplateResponseSchema'
      400:
        description: Validation error
      404:
        description: Template not found
    """
    # Get existing department template
    existing_department = department_service.get_department_template_by_id(template_id)
    if not existing_department:
        return ApiResponse.not_found("Department Template", template_id)

    # PATCH = Partial update - merge only provided fields
    # Use exclude_unset=True to allow setting fields to None while excluding unprovided fields
    update_data = data.model_dump(exclude_unset=True)
    update_data["id"] = template_id

    # Create updated entity by merging existing with provided fields
    updated_entity = DepartmentTemplateEntity.model_validate(
        {**existing_department.model_dump(), **update_data}
    )

    # Use service to update department template
    updated_department = department_service.update_department_template(updated_entity)

    # Convert to response schema using automatic conversion
    response_data = DepartmentTemplateResponseSchema.model_validate(updated_department)

    return ApiResponse.success(response_data)
