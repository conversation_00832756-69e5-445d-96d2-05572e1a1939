# Property Onboarding and Utility API
"""
Property Onboarding and Utility operations.

This module handles:
- Property onboarding with template-based entity creation
- Health check endpoint
- Other utility operations
"""

from flask import Blueprint
import logging

from cataloging_service.common.api_responses import ApiResponse, api_endpoint
from cataloging_service.common.request_decorators import parse_body
from cataloging_service.domain.services.template import PropertyOnboardingService
from cataloging_service.schemas.transaction_schemas import PropertyOnboardingRequestSchema
from object_registry import inject

bp = Blueprint("template_onboarding", __name__)
logger = logging.getLogger(__name__)


@bp.route("/property-onboarding", methods=["POST"])
@inject(onboarding_service=PropertyOnboardingService)
@parse_body(PropertyOnboardingRequestSchema)
@api_endpoint
def onboard_property(
    data: PropertyOnboardingRequestSchema,
    onboarding_service: PropertyOnboardingService
):
    """
    Onboard a property with template-based entity creation
    ---
    tags:
      - Template Management
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/PropertyOnboardingRequestSchema'
    responses:
      201:
        description: Property onboarded successfully
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PropertyOnboardingResponseSchema'
      400:
        description: Validation error
      404:
        description: Property not found
    """
    onboarding_entity = onboarding_service.onboard_property(data)
    return ApiResponse.created(onboarding_entity)