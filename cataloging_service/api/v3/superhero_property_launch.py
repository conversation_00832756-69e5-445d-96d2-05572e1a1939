import json

from flask import render_template
from flask import request
from flask_login import login_required
from marshmallow import ValidationError

from cataloging_service.api.v3.schemas.superhero_property_launch import PropertyLaunchSchema
from cataloging_service.constants.constants import COST_CENTER_CODE_MAPPING
from cataloging_service.domain import TenantConfigService
from cataloging_service.domain.enum_config_service import EnumConfigService
from cataloging_service.models import Brand
from core.common.api import BaseAPI
from core.common.api.api_response import response


class SuperheroPropertyLaunchAPI(BaseAPI):
    @login_required
    def get(self):
        brands = json.loads(TenantConfigService().get_tenant_configs_v2(config_name=COST_CENTER_CODE_MAPPING)[0].config_value)
        regions = EnumConfigService().get_region_enum_values()
        return render_template(
            "superhero_property_launch.html",
            brands=list(brands.keys()),
            regions=regions
        )

    def post(self):
        data = self._nest_data(request.json)
        serializer = PropertyLaunchSchema().load(data)
        if serializer.errors:
            raise ValidationError(message="Invalid data", errors=serializer.errors)
        from core.property.superhero_property_launch import launch_property

        new_property, _, _ = launch_property(PropertyLaunchSchema().dump(serializer.data))
        response.data = {"property_id": new_property.id}
        return response

    def _nest_data(self, data):
        address_data = {
            k.replace("address_", ""): v for k, v in data.items() if k.startswith("address_")
        }

        legal_address_data = {
            k.replace("legal_address_", ""): v
            for k, v in data.items()
            if k.startswith("legal_address_")
        }
        facilities = {k: v for k, v in data.items() if k.startswith("is_")}
        data["address"] = address_data
        same_as_contact_address = data.get("same_as_contact_address", False)
        if same_as_contact_address:
            data["legal_address"] = address_data.copy()
        else:
            data["legal_address"] = legal_address_data

        data["facilities"] = facilities
        data["room_type_details"] = [
            r for r in data["room_type_details"] if int(r["is_room_type_available"])
        ]
        return data


@SuperheroPropertyLaunchAPI.exception_handler.register([ValidationError, ValueError])
def handle_validation_error(exc, context):
    response.add_error_from_exception(exc)
    response.status_code = 400
    return response
