import functools
import inspect
import logging
import string
from collections import defaultdict

from flask import current_app
from flask_caching import Cache, function_namespace

from cataloging_service.cache.multi_key_cache_manager import MultiKeyCacheManager
from cataloging_service.infrastructure import redis_cache

logger = logging.getLogger(__name__)


def get_arg_names(f):
    """Return arguments of function

    :param f:
    :return: String list of arguments
    """
    sig = inspect.signature(f)
    return [
        parameter.name
        for parameter in sig.parameters.values()
        if parameter.kind == parameter.POSITIONAL_OR_KEYWORD
    ]


def function_fully_qualified_name(f, args=None):
    # Seems like a bit buggy, regarding how values comes in args.
    # Copied from flask_caching/__init__.py
    valid_chars = set(string.ascii_letters + string.digits + "_.")
    delchars = "".join(c for c in map(chr, range(256)) if c not in valid_chars)
    null_control = (dict((k, None) for k in delchars),)

    """Attempts to returns unique namespace for function"""
    m_args = get_arg_names(f)

    module = f.__module__

    if m_args and m_args[0] == "cls" and not inspect.isclass(args[0]):
        raise ValueError(
            "When using `delete_memoized` on a "
            "`@classmethod` you must provide the "
            "class as the first argument"
        )

    if hasattr(f, "__qualname__"):
        name = f.__qualname__
    else:
        klass = getattr(f, "__self__", None)

        if klass and not inspect.isclass(klass):
            klass = klass.__class__

        if not klass:
            klass = getattr(f, "im_class", None)

        if not klass:
            if m_args and args:
                if m_args[0] == "self":
                    klass = args[0].__class__
                elif m_args[0] == "cls":
                    klass = args[0]

        if klass:
            name = klass.__name__ + "." + f.__name__
        else:
            name = f.__name__

    ns = ".".join((module, name))
    ns = ns.translate(*null_control)

    return ns


class CustomCache(Cache):
    cache_key_prefix = None
    override_mode = 'override'  # refreshes cache from db
    bypass_mode = 'bypass'
    classic_mode = 'classic'

    caches_per_models = defaultdict(list)

    @property
    def cache(self):
        """
        Overrides cache property from `Cache` superclass, and returns `Flask_Caching.Cache` object for current tenant.

        Technically, this `CustomCache` object's internal `cache` is not used for any purpose. It uses cache backend
        from other tenant specific Flask-Caching Cache instance registered, in `redis_cache` module

        :return:
        """
        app = current_app or self.app
        custom_cache = redis_cache.get_cache()
        return app.extensions["cache"][custom_cache]

    def memoize_unhashed_key(self, attribute_for_cache_key=None, timeout=None, make_name=None, unless=None):
        """Modified version of original `@cache.memoize` decorator. The original decorator uses hashing techique to
        generate and store memoized version of the function which is cached in the form of: `_memver`.
        That unnecessarily hits a `MGET` redis call every API call. Somehow, we've seen those MGET calls being costly
        often in newrelic.

        In current application we don't need that complexity, so to keep cache key genration simple, we're using
        custom memoize decorator, which stores cache key as plain string.

        Use this to cache the result of a function, taking its arguments into account in the cache key.

        :param attribute_for_cache_key: Name of function/method argument that will be used for cache_keys. The actual
                                        value for cache key will be retrieved when the function is called,
                                        ideally using kwargs. If positional arguments (args) is used for function
                                        call, there should be only 1 of them. For more than 1 positional argument,
                                        the cache key manager cannot figure out the cache key.
        :param timeout: Default None. If set to an integer, will cache for that
                        amount of time. Unit of time is in seconds.
        :param make_name: Default None. If set this is a function that accepts
                          a single argument, the function name, and returns a
                          new string to be used as the function name.
                          If not set then the function name is used.
        :param unless: Default None. Cache will be bypassed, if this callable is true.
        """

        def memoize(f):
            @functools.wraps(f)
            def decorated_function(*args, **kwargs):
                if self._bypass_cache(unless, f, *args, **kwargs):
                    return f(*args, **kwargs)
                try:

                    if attribute_for_cache_key is None:
                        cache_key = self._make_cache_keys(None, f, *args, **kwargs)
                    else:
                        cache_key = self._make_cache_keys(attribute_for_cache_key, f, *args, **kwargs)

                    rv = self.cache.get(cache_key)
                    found = True

                    # If the value returned by cache.get() is None, it
                    # might be because the key is not found in the cache
                    # or because the cached value is actually None. Here we assume that key is not there
                    if rv is None:
                        found = False

                except Exception:
                    if self.app.debug:
                        raise
                    logger.exception("Exception possibly due to cache backend.")
                    return f(*args, **kwargs)

                if not found:
                    rv = f(*args, **kwargs)

                    try:
                        self.cache.set(
                            cache_key,
                            rv,
                            timeout=timeout,
                        )
                    except Exception:
                        if self.app.debug:
                            raise
                        logger.exception(
                            "Exception possibly due to cache backend."
                        )
                return rv

            return decorated_function

        return memoize

    def multi_key_memoize(self, attribute_for_cache_key, cache_key_id_getter, timeout=None, mode=None, unless=None):
        """
        Memoizes functions with multiple keys as input.

        @cache.multi_key_memoize(manager: SomeManager)
        def do_something(hotel_ids):
            return {h1: <large_payload>, h2: <large_payload>, h3: <large_payload>}

        This stores the result in cache. If a request comes in for h4 the h1, h2, h3 are pulled from cache and
        do_something will be only called for h4.

        :param attribute_for_cache_key:
            Name of function/method argument that will be used for cache_keys. The actual value for cache key will be
            retrieved when the function is called, ideally using kwargs.

            If positional arguments (args) is used for function call, there should be only 1 of them. For more than 1
            positional argument, the cache key manager cannot figure out the cache key.

        :param cache_key_id_getter:
            A lambda function that can retrieve the cache_key value from the Item that is stored in cache. It can
            retrieve an attribute value of an object, or a value corresponding to a key from dictionary. The value
            from this function should match the value passed as function argument against `attribute_for_cache_key`.

        :param timeout:
        :param mode:
            Three modes are supported:
                a. classic mode: Check cache -> return from cache -> Not in cache -> return from db, store in cache
                b. override mode: Skips cache -> return from db, store in cache
                c. bypass mode: Skips cache -> return from db(does *NOT* store in cache)
        :param unless: Default None. Cache will be bypassed, if this callable is true.
        """
        mode = mode if mode else self.classic_mode

        def memoize(f):

            @functools.wraps(f)
            def decorated_function(*args, **kwargs):
                if self._bypass_cache(unless, f, *args, **kwargs):
                    return f(*args, **kwargs)

                keys_to_cache_keys_dict = self._make_cache_keys(attribute_for_cache_key, f, *args, multi_key=True,
                                                                **kwargs)
                cache_manager = MultiKeyCacheManager(cache=self, attribute_for_cache_key=attribute_for_cache_key,
                                                     keys_to_cache_keys_dict=keys_to_cache_keys_dict,
                                                     cache_key_id_getter=cache_key_id_getter, timeout=timeout)

                cached_result, uncached_result = [], []

                if mode == self.classic_mode:
                    cached_result = cache_manager.get_cache()
                    if cache_manager.cache_miss_keys:
                        uncached_result = cache_manager.get_response_for_missed_keys(f, args, kwargs)
                else:
                    uncached_result = f(*args, **kwargs)

                result = cached_result + uncached_result

                if cached_result and not uncached_result:
                    logger.info('Using cached result')
                elif uncached_result and not cached_result:
                    logger.info('Using uncached result')
                elif cached_result and uncached_result:
                    logger.info('Using a mix of cached and uncached result')

                if mode == self.override_mode:
                    cache_manager.set_cache(result)
                elif cache_manager.cache_miss_keys:
                    cache_manager.set_cache(uncached_result)

                return result

            return decorated_function

        return memoize

    def clear_cache_on_model_change(self, model_classes_with_cache_key_getter, default_memoize=False):
        """
        Use this decorator to register the cached_function, with the models, which affects the return value of the
        cached_function. For each of those model classes, provide a cache_key_id_getter also, which would be used to
        get the cache_key_id, from that model. The cache_key_id would be the one used by cached_function,
        to cache result against multiple arguments

        For e.g.: If we're caching a function/method like below:

            def cache_func(self, property_ids):
                return [x]

        And we're caching the result of that function against each value of `property_ids` with function name as
        cache key prefix like below:

            - cache_func_property_id_1
            - cache_func_property_id_2

        And this cache_function return value is computed based on 2 model classes -> `Property` and `Location`,
        then the dictionary should contain:

            # Note that, `Location` and `Property` as <class> instance, instead of string, or an object of those models
            model_classes_with_cache_key_prefix = {
                Location: lambda x: x.property_id,
                Property: lambda x: x.id
            }

        `cache_key_id_getter` for Location would be `item.property_id`, as `Location` model has `property_id` attribute
        which is same as `property_ids` passed to `cache_func`. Similarly, `cache_key_id_getter` for `Property` would
        be `item.id`

        There can be scenario where a model class which affects return value of this method might be associated with
        multiple entities, to which the `cache_key` belongs. In that case, `cache_key_id_getter` would return a list.

        :param model_classes_with_cache_key_getter:
            {
                Location: lambda x: x.property_id,
                Property: lambda x: x.id
                City: lambda x: [loc.property_id for loc in x.locations],
            }
        :param default_memoize: Pass True, if using `@cache.memoize` decorator. False if using
            `@cache.multi_key_memoize` decorator
        :return:
        """

        def _attach_with_models(f):
            for model_class, cache_key_id_getter in model_classes_with_cache_key_getter.items():
                self.caches_per_models[model_class].append((f, default_memoize, cache_key_id_getter))

            return f

        return _attach_with_models

    def expire_cache(self, model_updated):
        """
        This method fetches all the cached_functions, associated with model_updated, along with their cache keys,
        and clears them. These cached_functions would have already been registered themselves against this model, using
        @cache.clear_cache_on_model_change decorator, defined above

        :param model_updated:
        :return:
        """
        model_class = model_updated.__class__
        cached_functions = self.caches_per_models.get(model_class)
        logger.info("Model updated: %s, model class: %s, cached_functions: %s", model_updated, model_class,
                    cached_functions)
        if not cached_functions:
            return

        for f, default_memoize, cache_key_id_getter in cached_functions:
            if not cache_key_id_getter:
                continue

            cache_key_ids = cache_key_id_getter(model_updated)

            if default_memoize:
                if cache_key_ids is None:
                    self.delete_memoized(f)
                elif isinstance(cache_key_ids, tuple):
                    self.delete_memoized(f, *cache_key_ids)
                else:
                    for cache_key_id in cache_key_ids:
                        self.delete_memoized(f, cache_key_id)
            else:
                logger.info("Function: %s. Cache Key Ids: %s", f, cache_key_ids)
                if cache_key_ids is None:
                    cache_keys = [self._make_cache_key(f)]
                elif isinstance(cache_key_ids, list):
                    cache_keys = [self._make_cache_key(f, cache_key_id) for cache_key_id in cache_key_ids]
                else:
                    cache_key_id = cache_key_ids
                    cache_keys = [self._make_cache_key(f, cache_key_id)]

                self.delete_many(*cache_keys)

    def _make_cache_keys(self, attribute_for_cache_key, f, *args, multi_key=False, **kwargs):
        spec = inspect.getfullargspec(f)
        if args:
            if spec.args[0] != 'self':
                if len(args) > 0 and not attribute_for_cache_key:
                    raise RuntimeError(
                        "Please provide attribute_for_cache_key argument for caching functions that take arguments")
                if len(args) > 1:
                    raise RuntimeError(
                        " Multiple Positional arguments are not supported. Use named arguments or single positional "
                        "argument, when calling the cached function. ")
            if spec.args[0] == 'self':
                if len(args) > 1 and not attribute_for_cache_key:
                    raise RuntimeError(
                        "Please provide attribute_for_cache_key argument for caching functions that take arguments")
                if len(args) > 2:
                    raise RuntimeError(
                        " Multiple Positional arguments are not supported. Use named arguments or single positional "
                        "argument, when calling the cached function. ")

        if attribute_for_cache_key is None:
            return self._make_cache_key(f)

        argument_for_keys = kwargs.get(attribute_for_cache_key)
        if not argument_for_keys:
            argument_for_keys = next(
                (value for value, arg_name in zip(args, spec.args) if arg_name == attribute_for_cache_key),
                None)
        if not argument_for_keys:
            raise RuntimeError("Cannot figure out cache_key_id from function arguments, and attribute_for_cache_key")

        if not multi_key:
            return self._make_cache_key(f, argument_for_keys)

        keys_to_cache_keys_dict = {k: self._make_cache_key(f, k) for k in argument_for_keys}
        return keys_to_cache_keys_dict

    def _make_cache_key(self, f, cache_key_id=None):
        fname = function_fully_qualified_name(f, cache_key_id)
        prefix = fname
        prefix = '{pfx}_'.format(pfx=prefix)
        return prefix + str(cache_key_id) if cache_key_id is not None else prefix
