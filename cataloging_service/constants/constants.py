import itertools
from enum import Enum

from wtforms.validators import <PERSON><PERSON><PERSON>

from cataloging_service.constants.model_choices import GoogleDriveFileTypeChoices

FULL_DAY_CHARGE = 'Full day charge'
ADMIN_ACCESS_ROLE = 'admin_administrator'
SUPER_USER_ROLE = 'admin_superuser'
READER_ROLE = 'admin_reader'
SUPER_USER_VIEW_ACCESSIBLE_ROLES = [SUPER_USER_ROLE]
ADMIN_VIEW_ACCESSIBLE_ROLES = [ADMIN_ACCESS_ROLE, SUPER_USER_ROLE, READER_ROLE]
USER_PASSWORD_COLUMN_NAME = 'password'
ADMIN_NAME = 'Catalog by Treebo'
ADMIN_TEMPLATE = 'bootstrap3'
ADMIN_URL = '/cataloging-service/admin'
FILE_ID_COLUMN_NAME = 'file_id'
FILE_TYPE_COLUMN_NAME = 'file_type'
FILE_NAME_COLUMN_NAME = 'file_name'
DOCUMENT_FOLDER_NAME = 'Document'
IMAGE_FOLDER_NAME = 'Image'
COMMON_FILE_PREFIX = 'Common'
COMMON_FOLDER_NAME = 'Common'

MIME_TYPE_FOLDER_MAPPING = {'application/pdf': DOCUMENT_FOLDER_NAME, 'image/jpeg': IMAGE_FOLDER_NAME,
                            'image/png': IMAGE_FOLDER_NAME}

FILE_UPLOAD_ALLOWED_EXTENSIONS = ['png', 'jpg', 'jpeg', 'pdf']
IMAGE_UPLOAD_ALLOWED_EXTENSIONS = ('png', 'jpg', 'jpeg',)
FILE_UPLOAD_EXTENSIONS = ('zip',)

GRAPHWIZ_MODEL_NAMES = ['Property', 'BankDetail', 'PropertyDetail', 'SystemProperty', 'Country', 'Cluster', 'State',
                        'City', 'MicroMarket', 'Locality', 'Location', 'RoomType', 'Room', 'RoomTypeConfiguration',
                        'GuestFacingProcess', 'GuestType', 'Owner', 'AmenityPublicWashroom', 'AmenityElevator',
                        'AmenityParking', 'AmenityDisableFriendly', 'AmenitySwimmingPool', 'AmenityGym', 'AmenitySpa',
                        'AmenityInHouseLaundry', 'Cuisine', 'AmenityBreakfast', 'AmenityPayment', 'PropertyAmenity',
                        'Bar', 'Restaurant', 'BanquetHall', 'AmenityIntercom', 'AmenityHotWater', 'AmenityTV',
                        'AmenityAC', 'AmenityStove', 'AmenityTwinBed', 'RoomAmenity', 'Landmark', 'Description',
                        'NeighbouringPlace', 'GoogleDriveBaseFolder', 'GoogleDriveFile', 'User', 'Role',
                        'guest_type_property', 'Ownership', 'breakfast_cuisine', 'restaurant_cuisine', 'roles_users']

GOOGLE_DRIVE_ALL_SCOPE = 'https://www.googleapis.com/auth/drive'
GOOGLE_SHEET_READ_SCOPE = 'https://www.googleapis.com/auth/spreadsheets.readonly'
GOOGLE_CLIENT_MAX_RETRY = 4
DEFAULT_EMAIL_SENDER = '<EMAIL>'
STATUS_OK = 200
CATALOGING_SERVICE_EMAIL_CONSUMER = 'CATALOGING_SERVICE'
PRODUCTION_ENVIRONMENT = 'PRODUCTION'

# Configuration Constants
CONFIG_PROJECT_ROOT = 'PROJECT_ROOT'
CONFIG_GOOGLE_DRIVE_KEY_FILE = 'GOOGLE_DRIVE_KEY_FILE'
CONFIG_GOOGLE_DRIVE_ROOT_FOLDER_ID = 'GOOGLE_DRIVE_ROOT_FOLDER_ID'
CONFIG_ENVIRONMENT = 'ENVIRONMENT'
CONFIG_DEV_EMAILS = 'DEV_EMAIL_PERMITTED_RECIPIENT_SET'
CONFIG_ERROR_MAIL_RECIPIENTS = 'ERROR_MAIL_RECIPIENTS'
CONFIG_PROPERTY_SIGNED_MAIL_RECIPIENTS = 'PROPERTY_SIGNED_RECIPIENTS'
RECCE_NOTIFICATION_TYPE = 'RECCE_TEAM'
BUSINESS_STAKE_HOLDER_TYPE = 'BUSINESS_STAKE_HOLDERS'
CONTENT_TEAM = 'CONTENT_TEAM'
ZIP_DIRECTORY = 'BASE_ZIP_DIRECTORY'
S3_ACCESS_KEY = 'S3_ACCESS_KEY'
S3_SECRET_KEY = 'S3_SECRET_KEY'
IMAGE_BUCKET_NAME = 'IMAGE_BUCKET_NAME'
VIDEO_UPLOAD_FOLDER_NAME = 'Common'
ALLOWED_VIDEO_EXTENSIONS = ('mp4', 'mov', 'wmv', 'flv', 'avi', 'avchd', 'mkv', 'gif')

LAUNCH_FILES = {GoogleDriveFileTypeChoices.FILE_TYPE_COMPLETE_AGREEMENT,
                GoogleDriveFileTypeChoices.FILE_TYPE_AGREEMENT_FIRST_PAGE,
                GoogleDriveFileTypeChoices.FILE_TYPE_AGREEMENT_EXPIRY,
                GoogleDriveFileTypeChoices.FILE_TYPE_AGREEMENT_DETAILS,
                GoogleDriveFileTypeChoices.FILE_TYPE_OTA_NOC,
                GoogleDriveFileTypeChoices.FILE_TYPE_LEASE_DOCUMENT,
                GoogleDriveFileTypeChoices.FILE_TYPE_ELECTRIC_BILL,
                GoogleDriveFileTypeChoices.FILE_TYPE_PARTNER_CANCELLED_CHEQUE,
                GoogleDriveFileTypeChoices.FILE_TYPE_HOTEL_REGISTRATION_DOCUMENT,
                GoogleDriveFileTypeChoices.FILE_TYPE_PAN_CARD,
                GoogleDriveFileTypeChoices.FILE_TYPE_SERVICE_TAX_ONE}

SIGN_FILES = {GoogleDriveFileTypeChoices.FILE_TYPE_COMPLETE_AGREEMENT,
              GoogleDriveFileTypeChoices.FILE_TYPE_AGREEMENT_FIRST_PAGE,
              GoogleDriveFileTypeChoices.FILE_TYPE_AGREEMENT_EXPIRY, GoogleDriveFileTypeChoices.FILE_TYPE_OTA_NOC,
              GoogleDriveFileTypeChoices.FILE_TYPE_HOTEL_REGISTRATION_DOCUMENT}

MIN_ROOM_COUNT = 10
MAX_PROPERTY_ID_LEN = 7
IFSC_PATTERN = '^[A-Za-z]{4}\d{7}$'
SWIFT_PATTERN = '^[A-Z]{6}[A-Z0-9]{2}([A-Z0-9]{3})?$'
PAN_PATTERN = '^[A-Za-z0-9]{10}$'
NAVISION_CODE_PATTERN = '^[A-Za-z0-9]{6,}$'
EMAIL_PATTERN = '^[^@]+@[^@]+\.[^@]+$'
TRANSLATOR_PATTERN = '(?:^|(?<=))((?:OAK|maple|acacia|mahogany)-(?:\d{1})-\d)(?:(?=,)|$)'  # oak-2-1,maple-2-1
MAX_PROPERTY_ID_RETRY = 3
ERROR_MSG_LENGTH = 1000
ITS_CREATE_OR_UPDATE_OTA_URI = '/create_or_update_ota'
ITS_CREATE_OR_UPDATE_OTA_MAPPINGS = '/create_or_update_ota_mapping'
UNIRATE_CREATE_OTA_URI = '/cm/api/v3/otas/'
UNIRATE_CREATE_MAPPINGS_URI = '/cm/api/v3/tenants/%s/hotels/'
PROMO_TRIGGER_URI = '/promo_push_to_cm'
RATE_TRIGGER_URI = '/v1/rackrate_push_to_cm/'
ITS_TRIGGER_URI = '/v1/inventory/bulk-sync-cm/'
DEFAULT_DATE_FORMAT = '%Y-%m-%d'
REVENUE_OPS_NOTIFICATION_TYPE_SUCCESS = 'REVENUE_OPS_TEAM_SUCCESS'
REVENUE_OPS_NOTIFICATION_TYPE_FAILURE = 'REVENUE_OPS_TEAM_FAILURE'
CONFIG_TREEBO_TENANT_CODE = 'TREEBO_TENANT_CODE'
CONFIG_CDN_HOST = 'CDN_HOST'
CACHING_MODES = Enum('CACHING_MODES', 'bypass, override, fetch')
PMS = Enum('PMS', 'hx, crs')
RESELLER = 'RESELLER'
MARKETPLACE = 'MARKETPLACE'
SHIELD_DETAILS = ['GOLD','BRONZE','PLATINUM']

# todo: find the max occupancy room config and use range of that
_ASSUMED_MAX_OCCUPANCY_OF_ANY_ROOM = 15
ALL_POSSIBLE_ROOM_CONFIGS = ['{}-{}'.format(adult, child) for adult, child in
                             itertools.product(range(0, _ASSUMED_MAX_OCCUPANCY_OF_ANY_ROOM), repeat=2)]

TREEBO_TENANT = 'treebo'

CASHIERING_ENABLED = 'cashiering_enabled'

NON_INCLUSION_SKU_CATEGORIES = ['stay', 'miscellaneous']
LINKAGE_PERCENTAGE_CHANGE_NUMBER = 100

YOUTUBE_VIDEO_URL_REGEXP = Regexp(
    regex=r"^((?:https?:)?\/\/)?((?:www|m)\.)?((?:youtube(-nocookie)?\.com|youtu.be))"
          r"(\/(?:[\w\-]+\?v=|embed\/|live\/|v\/)?)([\w\-]+)(\S+)?$",
    message="Please provide the correct YouTube Video URL."
)


class S3ACL(Enum):
    BUCKET_OWNER_FULL_CONTROL = 'bucket-owner-full-control'


DYNAMIC_PRICE_SKU_CATEGORY_TO_PREFIXES = {
    'stay': [
        "ECI-BT69",  # Early check-in between 6 AM and 9 AM
        "ECI-B6",    # Early check-in before 6 AM
        "ECI-A9",    # Early check-in after 9 AM
        "LCO-BT122",  # Late Check-out between 12 PM and 2 PM
        "LCO-BT24",  # Late Check-out between 2 PM and 4 PM
        "LCO-A4",    # Late Check-out after 4 PM
    ]
}
EXTRA_ADULT = "EXTRA-ADULT"
DEFAULT_RACK_RATE_FOR_DYNAMIC_PRICING_SKUS = 200

CITY_CODE_MAPPING_FOR_COST_CENTER_ID = 'city_code_mapping_for_cost_center_id'
COST_CENTER_CODE_MAPPING = 'cost_center_brand_mapping'
DEFAULT_COST_CENTER_ID_BRAND_CODE = 'default_cost_center_brand_code'
FINANCE_SUPPORT_MAIL = 'finance_support_mail'


class CostCenterConfig(Enum):
    REGIONS = 'regions'
