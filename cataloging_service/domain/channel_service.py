import logging

from cataloging_service.constants import error_codes
from cataloging_service.exceptions import CatalogingServiceException
from cataloging_service.infrastructure.decorators import atomic_operation
from cataloging_service.infrastructure.messaging.messaging_wrappers import ChannelWrapper, SubChannelWrapper, \
    ApplicationWrapper, PricingPolicyWrapper
from cataloging_service.models import PricingMapping

logger = logging.getLogger(__name__)


class ChannelService:
    def __init__(self, channel_repository, messaging_service):
        self.__channel_repository = channel_repository
        self.__messaging_service = messaging_service

    def get_channel(self, id):
        channel = self.__channel_repository.get_channel(id, eager_load=True, with_applications=True,
                                                        with_subchannels=True)

        if not channel:
            raise CatalogingServiceException(error_codes.CHANNEL_NOT_FOUND, context='channel id: %s' % id)

        return channel

    def get_all_channels(self, ids=None):
        return self.__channel_repository.get_all_channels(ids)

    def sget_all_pricing_policies(self, ids=None):
        return self.__channel_repository.rget_all_pricing_policies(ids)

    def get_channel_for_sub_channel(self, sub_channel_id):
        channel = self.__channel_repository.get_channel_for_sub_channel(sub_channel_id)

        if not channel:
            raise CatalogingServiceException(error_codes.CHANNEL_NOT_FOUND,
                                             context='sub channel id: %s' % sub_channel_id)

        return channel

    def get_sub_channels(self, channel_id):
        channel = self.__channel_repository.get_channel(channel_id, eager_load=True, with_applications=False,
                                                        with_subchannels=True)
        return channel.sub_channels

    def get_all_applications(self, ids=None):
        try:

            return self.__channel_repository.get_all_applications(ids)
        except Exception as e:
            logger.error(e)
            raise CatalogingServiceException(error_codes.INTERNAL_SERVER_ERROR)

    def get_applications(self, channel_id):
        channel = self.__channel_repository.get_channel(channel_id, eager_load=True, with_applications=True,
                                                        with_subchannels=False)
        return channel.applications

    def scheck_if_pricing_policy_is_unique(self):
        policies = self.__channel_repository.rget_default_pricing_policies()
        return False if len(policies) > 1 else True

    @atomic_operation
    def supdate_pricing_policy_for_sub_channel(self, model):
        default_pricing_policy = self.__channel_repository.rget_default_pricing_policy()
        pricing_mapping = PricingMapping(pricing_id=default_pricing_policy.id,
                                         channel_id=model.channel_id,
                                         sub_channel_id=model.id)
        self.__channel_repository.persist(pricing_mapping)

    def publish_channels(self, ids):
        channels = self.get_all_channels(ids)
        messages = [ChannelWrapper(channel, True, False).get_json() for channel in channels]
        for message in messages:
            self.__messaging_service.publish_channel_message(message)

    def publish_sub_channels(self, ids):
        sub_channels = self.__channel_repository.get_all_sub_channels(ids)
        messages = [SubChannelWrapper(sub_channel, True, False).get_json() for sub_channel in sub_channels]
        for message in messages:
            self.__messaging_service.publish_sub_channel_message(message)

    def publish_applications(self, ids):
        applications = self.__channel_repository.get_all_applications(ids)
        messages = [ApplicationWrapper(application, True, False).get_json() for application in applications]
        for message in messages:
            self.__messaging_service.publish_application_message(message)

    def publish_pricing_policies(self, ids):
        pricing_policies = self.__channel_repository.rget_all_pricing_policies(ids)
        messages = [PricingPolicyWrapper(policy, True, False).get_json() for policy in pricing_policies]
        for message in messages:
            self.__messaging_service.publish_pricing_message(message)
