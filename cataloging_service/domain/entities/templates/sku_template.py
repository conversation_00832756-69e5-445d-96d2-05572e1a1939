"""
SKU Template Domain Entity
Brand-scoped SKU template with comprehensive business rules and validation.
"""

from datetime import datetime
from decimal import Decimal
from typing import Optional, List, Dict, Any

from pydantic import Field, field_validator
from pydantic.types import PositiveInt, NonNegativeInt

from cataloging_service.domain.entities.base_entity import BaseDomainEntity


class SkuTemplateEntity(BaseDomainEntity):
    """Brand-scoped SKU template domain entity"""

    id: Optional[PositiveInt] = None
    code: str = Field(
        ..., min_length=2, max_length=50, description="Unique SKU template code"
    )
    name: str = Field(
        ..., min_length=1, max_length=255, description="SKU template name"
    )
    display_name: Optional[str] = Field(
        None, max_length=255, description="Display name for SKU"
    )
    description: Optional[str] = Field(
        None, max_length=1000, description="SKU description"
    )
    hsn_sac: Optional[str] = Field(
        None, max_length=20, description="HSN/SAC code for taxation"
    )
    category_id: PositiveInt = Field(..., description="SKU category ID")
    department_template_code: str = Field(
        ..., max_length=50, description="Department template code"
    )
    profit_center_template_code: str = Field(
        ..., max_length=50, description="Profit center template code"
    )
    is_active: bool = Field(True, description="Whether template is active")
    auto_create_on_property_launch: bool = Field(
        False, description="Auto-create on property launch"
    )
    is_modular: bool = Field(False, description="Whether SKU is modular")
    saleable: bool = Field(True, description="Whether SKU is saleable")
    chargeable_per_occupant: bool = Field(False, description="Charge per occupant")
    is_property_inclusion: bool = Field(False, description="Property inclusion item")
    tax_at_room_rate: bool = Field(False, description="Tax at room rate")
    default_list_price: Optional[Decimal] = Field(
        None, ge=0, description="Default list price"
    )
    default_sale_price: Optional[Decimal] = Field(
        None, ge=0, description="Default sale price"
    )
    tax_type: Optional[str] = Field(None, max_length=50, description="Tax type")
    sku_type: Optional[str] = Field(None, max_length=50, description="SKU type")
    tag: Optional[str] = Field(None, max_length=100, description="SKU tag")
    identifier: Optional[str] = Field(
        None, max_length=400, description="SKU identifier"
    )
    sku_count: NonNegativeInt = Field(0, description="SKU count")
    flat_count_for_creation: NonNegativeInt = Field(
        0, description="Flat count for creation"
    )
    offering: Dict[str, Any] = Field(
        default_factory=dict, description="Offering configuration"
    )
    frequency: Dict[str, Any] = Field(
        default_factory=dict, description="Frequency configuration"
    )
    template_config: Dict[str, Any] = Field(
        default_factory=dict, description="Template configuration"
    )
    sku_details: Dict[str, Any] = Field(
        default_factory=dict, description="Additional SKU details"
    )
    created_at: Optional[datetime] = None
    modified_at: Optional[datetime] = None

    @field_validator("code")
    @classmethod
    def validate_code(cls, v: str) -> str:
        if not v.replace("_", "").replace("-", "").isalnum():
            raise ValueError(
                "Code must contain only alphanumeric characters, hyphens, and underscores"
            )
        return v.upper()

    @field_validator("department_template_code", "profit_center_template_code")
    @classmethod
    def validate_template_codes(cls, v: str) -> str:
        if not v.replace("_", "").replace("-", "").isalnum():
            raise ValueError(
                "Template code must contain only alphanumeric characters, hyphens, and underscores"
            )
        return v.upper()

    def validate_template_associations(
        self, department_templates: List, profit_center_templates: List
    ) -> bool:
        """Business rule: Validate that associated templates exist and are active"""
        # Import here to avoid circular imports
        from cataloging_service.domain.entities.templates.department_template import (
            DepartmentTemplateEntity,
        )
        from cataloging_service.domain.entities.templates.profit_center_template import (
            ProfitCenterTemplateEntity,
        )

        # Find department template
        department_template = next(
            (
                dt
                for dt in department_templates
                if isinstance(dt, DepartmentTemplateEntity)
                and dt.code == self.department_template_code
            ),
            None,
        )

        if not department_template or not department_template.is_active:
            return False

        # Find profit center template
        profit_center_template = next(
            (
                pct
                for pct in profit_center_templates
                if isinstance(pct, ProfitCenterTemplateEntity)
                and pct.code == self.profit_center_template_code
                and pct.department_template_code == self.department_template_code
            ),
            None,
        )

        return profit_center_template is not None and profit_center_template.is_active

    def can_auto_create(self) -> bool:
        """Business rule: Determine if SKU can be auto-created"""
        return self.auto_create_on_property_launch and self.is_active and self.saleable

    def requires_pricing(self) -> bool:
        """Business rule: Check if SKU requires pricing information"""
        return self.saleable and not self.is_property_inclusion

    def is_taxable(self) -> bool:
        """Business rule: Check if SKU is taxable"""
        return self.tax_type is not None and len(self.tax_type.strip()) > 0
