"""
Transaction Default Mapping Domain Entity
"""

from datetime import datetime
from typing import Optional, Dict, Any

from pydantic import Field

from cataloging_service.domain.entities.base_entity import BaseDomainEntity

class TransactionDefaultMappingEntity(BaseDomainEntity):
    id: int = Field(..., description="Unique ID")
    brand_id: int = Field(..., description="Brand ID this mapping belongs to")
    transaction_type: str = Field(..., description="Transaction type")
    transaction_type_code: str = Field(..., description="Specific transaction type code")
    entity_type: str = Field(..., description="Entity type or scope of the transaction")
    default_gl_code: Optional[str] = Field(None, description="Default GL code")
    default_erp_id: Optional[str] = Field(None, description="Default ERP ID")
    default_particulars: Optional[str] = Field(None, description="Default particulars")
    default_is_merge: bool = Field(False, description="Default merge flag")
    transaction_details: Dict[str, Any] = Field(
        default_factory=dict, description="Default transaction details JSON"
    )
    is_active: bool = Field(True, description="Whether mapping is active or inactive")
    created_at: Optional[datetime] = None
    modified_at: Optional[datetime] = None
