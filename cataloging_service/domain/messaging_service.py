from kombu.entity import Exchange

from cataloging_service.constants import messaging_constants
from cataloging_service.constants.messaging_constants import PropertyMessageActions
from cataloging_service.infrastructure.messaging.messaging_wrappers import PropertyMessagingWrapper, \
    RoomMessagingWrapper, \
    RoomTypeConfigMessagingWrapper, RestaurantMessagingWrapper, BarMessagingWrapper, BanquetHallWrapper, \
    PropertyAmenitiesWrapper, RoomAmenitiesWrapper, PropertyImageWrapper, AmenitySummaryWrapper, SkuCategoryWrapper, \
    ChannelWrapper, SubChannelWrapper, ApplicationWrapper, RoomTypeWrapper, CityWrapper, SkuWrapper, \
    PricingPolicyWrapper, PropertySkuWrapper, SellerWrapper, SellerSkuWrapper, RuptubLegalEntityDetailsWrapper, \
    MenuWrapper, ComboWrapper, ItemWrapper, RestaurantTableMessagingWrapper, PropertyVideoWrapper

from cataloging_service.models import Property, Room, RoomTypeConfiguration, Restaurant, Bar, BanquetHall, \
    PropertyAmenity, RoomAmenity, PropertyImage, AmenitySummary, SkuCategory, Channel, SubChannel, Application, \
    RoomType, City, Sku, PricingPolicy, PropertySku, Seller, SellerSku, RuptubLegalEntityDetails, Menu, Combo, Item,\
    RestaurantTable

from cataloging_service.models import Property, Room, RoomTypeConfiguration, Restaurant, Bar, BanquetHall, \
    PropertyAmenity, RoomAmenity, PropertyImage, AmenitySummary, SkuCategory, Channel, SubChannel, Application, \
    RoomType, City, Sku, PricingPolicy, PropertySku, Seller, SellerSku, RuptubLegalEntityDetails, PropertyVideo


class MessagingService:
    def __init__(self, message_publisher):
        self.message_publisher = message_publisher

    def publish_property_message(self, json_message):
        self.message_publisher.publish(json_message, Exchange(name=messaging_constants.CS_EXCHANGE_NAME,
                                                              type=messaging_constants.EXCHANGE_TYPE),
                                       messaging_constants.PROPERTY_ROUTING_KEY)

    def publish_room_message(self, json_message):
        self.message_publisher.publish(json_message, Exchange(name=messaging_constants.CS_EXCHANGE_NAME,
                                                              type=messaging_constants.EXCHANGE_TYPE),
                                       messaging_constants.ROOM_ROUTING_KEY)

    def publish_room_rack_rate_message(self, json_message):
        self.message_publisher.publish(json_message, Exchange(name=messaging_constants.CS_EXCHANGE_NAME,
                                                              type=messaging_constants.EXCHANGE_TYPE),
                                       messaging_constants.ROOM_RACK_RATE_ROUTING_KEY)

    def publish_room_type_config_message(self, json_message):
        self.message_publisher.publish(json_message, Exchange(name=messaging_constants.CS_EXCHANGE_NAME,
                                                              type=messaging_constants.EXCHANGE_TYPE),
                                       messaging_constants.ROOM_TYPE_CONFIG_ROUTING_KEY)

    def publish_property_amenity_message(self, json_message):
        self.message_publisher.publish(json_message, Exchange(name=messaging_constants.CS_EXCHANGE_NAME,
                                                              type=messaging_constants.EXCHANGE_TYPE),
                                       messaging_constants.PROPERTY_AMENITY_ROUTING_KEY)

    def publish_room_amenity_message(self, json_message):
        self.message_publisher.publish(json_message, Exchange(name=messaging_constants.CS_EXCHANGE_NAME,
                                                              type=messaging_constants.EXCHANGE_TYPE),
                                       messaging_constants.ROOM_AMENITY_ROUTING_KEY)

    def publish_property_image_message(self, json_message):
        self.message_publisher.publish(json_message, Exchange(name=messaging_constants.CS_EXCHANGE_NAME,
                                                              type=messaging_constants.EXCHANGE_TYPE),
                                       messaging_constants.PROPERTY_MEDIA_ROUTING_KEY)

    def publish_amenity_summary_message(self, json_message):
        self.message_publisher.publish(json_message, Exchange(name=messaging_constants.CS_EXCHANGE_NAME,
                                                              type=messaging_constants.EXCHANGE_TYPE),
                                       messaging_constants.AMENITY_SUMMARY_ROUTING_KEY)

    def publish_sku_category_message(self, json_message):
        self.message_publisher.publish(json_message, Exchange(name=messaging_constants.CS_EXCHANGE_NAME,
                                                              type=messaging_constants.EXCHANGE_TYPE),
                                       messaging_constants.SKU_CATEGORY_ROUTING_KEY)

    def publish_sku_message(self, json_message):
        self.message_publisher.publish(json_message, Exchange(name=messaging_constants.CS_EXCHANGE_NAME,
                                                              type=messaging_constants.EXCHANGE_TYPE),
                                       messaging_constants.SKU_ROUTING_KEY)

    def publish_property_sku_message(self, json_message):
        self.message_publisher.publish(json_message, Exchange(name=messaging_constants.CS_EXCHANGE_NAME,
                                                              type=messaging_constants.EXCHANGE_TYPE),
                                       messaging_constants.PROPERTY_SKU_ROUTING_KEY)

    def publish_sku_under_property_message(self, json_message):
        self.message_publisher.publish(json_message, Exchange(name=messaging_constants.CS_EXCHANGE_NAME,
                                                              type=messaging_constants.EXCHANGE_TYPE),
                                       messaging_constants.SKU_UNDER_PROPERTY_ROUTING_KEY)

    def publish_seller_message(self, json_message):
        self.message_publisher.publish(json_message, Exchange(name=messaging_constants.CS_EXCHANGE_NAME,
                                                              type=messaging_constants.EXCHANGE_TYPE),
                                       messaging_constants.SELLER_DETAILS_ROUTING_KEY)

    def publish_seller_sku_message(self, json_message):
        self.message_publisher.publish(json_message, Exchange(name=messaging_constants.CS_EXCHANGE_NAME,
                                                              type=messaging_constants.EXCHANGE_TYPE),
                                       messaging_constants.SELLER_SKU_ROUTING_KEY)

    def publish_channel_message(self, json_message):
        self.message_publisher.publish(json_message, Exchange(name=messaging_constants.CS_EXCHANGE_NAME,
                                                              type=messaging_constants.EXCHANGE_TYPE),
                                       messaging_constants.CHANNEL_ROUTING_KEY)

    def publish_sub_channel_message(self, json_message):
        self.message_publisher.publish(json_message, Exchange(name=messaging_constants.CS_EXCHANGE_NAME,
                                                              type=messaging_constants.EXCHANGE_TYPE),
                                       messaging_constants.SUB_CHANNEL_ROUTING_KEY)

    def publish_application_message(self, json_message):
        self.message_publisher.publish(json_message, Exchange(name=messaging_constants.CS_EXCHANGE_NAME,
                                                              type=messaging_constants.EXCHANGE_TYPE),
                                       messaging_constants.APPLICATION_ROUTING_KEY)

    def publish_pricing_message(self, json_message):
        self.message_publisher.publish(json_message, Exchange(name=messaging_constants.CS_EXCHANGE_NAME,
                                                              type=messaging_constants.EXCHANGE_TYPE),
                                       messaging_constants.PRICING_POLICY_ROUTING_KEY)

    def publish_room_type_message(self, json_message):
        self.message_publisher.publish(json_message, Exchange(name=messaging_constants.CS_EXCHANGE_NAME,
                                                              type=messaging_constants.EXCHANGE_TYPE),
                                       messaging_constants.ROOM_TYPE_ROUTING_KEY)

    def publish_city_message(self, json_message):
        self.message_publisher.publish(json_message, Exchange(name=messaging_constants.CS_EXCHANGE_NAME,
                                                              type=messaging_constants.EXCHANGE_TYPE),
                                       messaging_constants.CITY_ROUTING_KEY)

    def publish_ruptub_details_message(self, json_message):
        self.message_publisher.publish(json_message, Exchange(name=messaging_constants.CS_EXCHANGE_NAME,
                                                              type=messaging_constants.EXCHANGE_TYPE),
                                       messaging_constants.RUPTUB_ROUTING_KEY)

    def publish_restaurant_table_message(self, json_message):
        self.message_publisher.publish(json_message, Exchange(name=messaging_constants.CS_EXCHANGE_NAME,
                                                              type=messaging_constants.EXCHANGE_TYPE),
                                       messaging_constants.RESTAURANT_TABLE_ROUTING_KEY)

    def publish_menu_message(self, json_message):
        self.message_publisher.publish(json_message, Exchange(name=messaging_constants.CS_EXCHANGE_NAME,
                                                              type=messaging_constants.EXCHANGE_TYPE),
                                       messaging_constants.MENU_ROUTING_KEY)

    def publish_combo_message(self, json_message):
        self.message_publisher.publish(json_message, Exchange(name=messaging_constants.CS_EXCHANGE_NAME,
                                                              type=messaging_constants.EXCHANGE_TYPE),
                                       messaging_constants.COMBO_ROUTING_KEY)

    def publish_item_message(self, json_message):
        self.message_publisher.publish(json_message, Exchange(name=messaging_constants.CS_EXCHANGE_NAME,
                                                              type=messaging_constants.EXCHANGE_TYPE),
                                       messaging_constants.ITEM_ROUTING_KEY)

    def publish_property_video(self, json_message):
        self.message_publisher.publish(json_message, Exchange(name=messaging_constants.CS_EXCHANGE_NAME,
                                                              type=messaging_constants.EXCHANGE_TYPE),
                                       messaging_constants.PROPERTY_MEDIA_ROUTING_KEY)

    def create_publishable_messages(self, new_objects, modified_objects, deleted_objects, messages):
        for new_object in new_objects:
            if type(new_object) == Property:
                messages.insert_property_message(PropertyMessagingWrapper(
                    new_object, True, False, PropertyMessageActions.ADMIN_CREATE.value).get_json())
            elif type(new_object) == Room:
                messages.insert_room_messages(RoomMessagingWrapper(new_object, True, False).get_json())
            elif type(new_object) == RoomTypeConfiguration:
                messages.insert_room_config_messages(
                    RoomTypeConfigMessagingWrapper(new_object, True, False).get_json())
            elif type(new_object) == Restaurant:
                messages.insert_restaurant_messages(
                    RestaurantMessagingWrapper(new_object, True, False).get_json())
            elif type(new_object) == Bar:
                messages.insert_bar_messages(
                    BarMessagingWrapper(new_object, True, False).get_json())
            elif type(new_object) == BanquetHall:
                messages.insert_hall_messages(
                    BanquetHallWrapper(new_object, True, False).get_json())
            elif type(new_object) == PropertyAmenity:
                messages.insert_property_amenities(
                    PropertyAmenitiesWrapper(new_object, True, False).get_json())
            elif type(new_object) == RoomAmenity:
                messages.insert_room_amenities(
                    RoomAmenitiesWrapper(new_object, True, False).get_json())
            elif type(new_object) == PropertyImage:
                messages.insert_property_images(PropertyImageWrapper(new_object, True, False).get_json())
            elif type(new_object) == SkuCategory:
                messages.insert_sku_categories(SkuCategoryWrapper(new_object, True, False).get_json())
            elif type(new_object) == Channel:
                messages.insert_channels(ChannelWrapper(new_object, True, False).get_json())
            elif type(new_object) == SubChannel:
                messages.insert_sub_channels(SubChannelWrapper(new_object, True, False).get_json())
            elif type(new_object) == Application:
                messages.insert_applications(ApplicationWrapper(new_object, True, False).get_json())
            elif type(new_object) == RoomType:
                messages.insert_room_types(RoomTypeWrapper(new_object, True, False).get_json())
            elif type(new_object) == City:
                messages.insert_city(CityWrapper(new_object, True, False).get_json())
            elif type(new_object) == Sku:
                messages.insert_sku(SkuWrapper(new_object, True, False).get_json())
            elif type(new_object) == Seller:
                messages.insert_seller(SellerWrapper(new_object, True, False).get_json())
            elif type(new_object) == SellerSku:
                messages.insert_seller_sku(SellerSkuWrapper(new_object, True, False).get_json())
            elif type(new_object) == RuptubLegalEntityDetails:
                messages.insert_ruptub_details(RuptubLegalEntityDetailsWrapper(new_object, True, False).get_json())
            elif type(new_object) == Menu:
                messages.insert_menu(MenuWrapper(new_object, True, False).get_json())
            elif type(new_object) == Combo:
                messages.insert_combo(ComboWrapper(new_object, True, False).get_json())
            elif type(new_object) == Item:
                messages.insert_item(ItemWrapper(new_object, True, False).get_json())
            elif type(new_object) == PropertyVideo:
                messages.insert_property_video(PropertyVideoWrapper(new_object, True, False).get_json())

        for modified_object in modified_objects:
            if type(modified_object) == Property:
                messages.insert_property_message(
                    PropertyMessagingWrapper(modified_object, False, False,
                                             PropertyMessageActions.ADMIN_UPDATE.value).get_json())
            elif type(modified_object) == Room:
                messages.insert_room_messages(RoomMessagingWrapper(modified_object, False, False).get_json())
            elif type(modified_object) == RoomTypeConfiguration:
                messages.insert_room_config_messages(
                    RoomTypeConfigMessagingWrapper(modified_object, False, False).get_json())
            elif type(modified_object) == Restaurant:
                messages.insert_restaurant_messages(
                    RestaurantMessagingWrapper(modified_object, False, False).get_json())
            elif type(modified_object) == Bar:
                messages.insert_bar_messages(
                    BarMessagingWrapper(modified_object, False, False).get_json())
            elif type(modified_object) == BanquetHall:
                messages.insert_hall_messages(
                    BanquetHallWrapper(modified_object, False, False).get_json())
            elif type(modified_object) == PropertyAmenity:
                messages.insert_property_amenities(
                    PropertyAmenitiesWrapper(modified_object, False, False).get_json())
            elif type(modified_object) == RoomAmenity:
                messages.insert_room_amenities(
                    RoomAmenitiesWrapper(modified_object, False, False).get_json())
            elif type(modified_object) == PropertyImage:
                messages.insert_property_images(PropertyImageWrapper(modified_object, False, False).get_json())
            elif type(modified_object) == AmenitySummary:
                messages.insert_amenity_summaries(AmenitySummaryWrapper(modified_object, False, False).get_json())
            elif type(modified_object) == SkuCategory:
                messages.insert_sku_categories(SkuCategoryWrapper(modified_object, False, False).get_json())
            elif type(modified_object) == Channel:
                messages.insert_channels(ChannelWrapper(modified_object, False, False).get_json())
            elif type(modified_object) == SubChannel:
                messages.insert_sub_channels(SubChannelWrapper(modified_object, False, False).get_json())
            elif type(modified_object) == Application:
                messages.insert_applications(ApplicationWrapper(modified_object, False, False).get_json())
            elif type(modified_object) == RoomType:
                messages.insert_room_types(RoomTypeWrapper(modified_object, False, False).get_json())
            elif type(modified_object) == City:
                messages.insert_city(CityWrapper(modified_object, False, False).get_json())
            elif type(modified_object) == Sku:
                messages.insert_sku(SkuWrapper(modified_object, False, False).get_json())
            elif type(modified_object) == PricingPolicy:
                messages.insert_pricing_policy(PricingPolicyWrapper(modified_object, False, False).get_json())
            elif type(modified_object) == PropertySku:
                messages.insert_property_sku(PropertySkuWrapper(modified_object, False, False).get_json())
            elif type(modified_object) == Seller:
                messages.insert_seller(SellerWrapper(modified_object, False, False).get_json())
            elif type(modified_object) == SellerSku:
                messages.insert_seller_sku(SellerSkuWrapper(modified_object, False, False).get_json())
            elif type(modified_object) == RuptubLegalEntityDetails:
                messages.insert_ruptub_details(RuptubLegalEntityDetailsWrapper(
                    modified_object, False, False).get_json())
            elif type(modified_object) == Menu:
                messages.insert_menu(MenuWrapper(modified_object, False, False).get_json())
            elif type(modified_object) == Combo:
                messages.insert_combo(ComboWrapper(modified_object, False, False).get_json())
            elif type(modified_object) == Item:
                messages.insert_item(ItemWrapper(modified_object, False, False).get_json())
            elif type(modified_object) == PropertyVideo:
                messages.insert_property_video(PropertyVideoWrapper(modified_object, False, False).get_json())

        for deleted_object in deleted_objects:
            if type(deleted_object) == Property:
                messages.insert_property_message(PropertyMessagingWrapper(
                    deleted_object, False, True, PropertyMessageActions.ADMIN_DELETE.value).get_json())
            elif type(deleted_object) == Room:
                messages.insert_room_messages(RoomMessagingWrapper(deleted_object, False, True).get_json())
            elif type(deleted_object) == RoomTypeConfiguration:
                messages.insert_room_config_messages(
                    RoomTypeConfigMessagingWrapper(deleted_object, False, True).get_json())
            elif type(deleted_object) == Restaurant:
                messages.insert_restaurant_messages(
                    RestaurantMessagingWrapper(deleted_object, False, True).get_json())
            elif type(deleted_object) == Bar:
                messages.insert_bar_messages(
                    BarMessagingWrapper(deleted_object, False, True).get_json())
            elif type(deleted_object) == BanquetHall:
                messages.insert_hall_messages(
                    BanquetHallWrapper(deleted_object, False, True).get_json())
            elif type(deleted_object) == PropertyAmenity:
                messages.insert_property_amenities(
                    PropertyAmenitiesWrapper(deleted_object, False, True).get_json())
            elif type(deleted_object) == RoomAmenity:
                messages.insert_room_amenities(
                    RoomAmenitiesWrapper(deleted_object, False, True).get_json())
            elif type(deleted_object) == PropertyImage:
                messages.insert_property_images(PropertyImageWrapper(deleted_object, False, True).get_json())
            elif type(deleted_object) == Channel:
                messages.insert_channels(ChannelWrapper(deleted_object, False, True).get_json())
            elif type(deleted_object) == SubChannel:
                messages.insert_sub_channels(SubChannelWrapper(deleted_object, False, True).get_json())
            elif type(deleted_object) == Application:
                messages.insert_applications(ApplicationWrapper(deleted_object, False, True).get_json())
            elif type(deleted_object) == Sku:
                messages.insert_sku(SkuWrapper(deleted_object, False, True).get_json())
            elif type(deleted_object) == Seller:
                messages.insert_seller(SellerWrapper(deleted_object, False, True).get_json())
            elif type(deleted_object) == Menu:
                messages.insert_menu(MenuWrapper(deleted_object, False, True).get_json())
            elif type(deleted_object) == Combo:
                messages.insert_combo(ComboWrapper(deleted_object, False, True).get_json())
            elif type(deleted_object) == Item:
                messages.insert_item(ItemWrapper(deleted_object, False, True).get_json())
            elif type(deleted_object) == PropertyVideo:
                messages.insert_property_video(PropertyVideoWrapper(deleted_object, False, True).get_json())

        return messages

    def publish_all_messages(self, messages):
        for message in messages.properties:
            self.publish_property_message(message)

        for message in messages.room_configs:
            self.publish_room_type_config_message(message)

        for message in messages.rooms:
            self.publish_room_message(message)

        for message in messages.restaurants:
            self.publish_property_amenity_message(message)

        for message in messages.bars:
            self.publish_property_amenity_message(message)

        for message in messages.halls:
            self.publish_property_amenity_message(message)

        for message in messages.property_amenities:
            self.publish_property_amenity_message(message)

        for message in messages.room_amenities:
            self.publish_room_amenity_message(message)

        for message in messages.property_images:
            self.publish_property_image_message(message)

        for message in messages.amenity_summaries:
            self.publish_amenity_summary_message(message)

        for message in messages.sku_categories:
            self.publish_sku_category_message(message)

        for message in messages.channels:
            self.publish_channel_message(message)

        for message in messages.sub_channels:
            self.publish_sub_channel_message(message)

        for message in messages.applications:
            self.publish_application_message(message)

        for message in messages.room_types:
            self.publish_room_type_message(message)

        for message in messages.cities:
            self.publish_city_message(message)

        for message in messages.skus:
            self.publish_sku_message(message)

        for message in messages.pricing_policies:
            self.publish_pricing_message(message)

        for message in messages.property_skus:
            self.publish_property_sku_message(message)

        for message in messages.sellers:
            self.publish_seller_message(message)

        for message in messages.seller_sku:
            self.publish_seller_sku_message(message)

        for message in messages.ruptub_details:
            self.publish_ruptub_details_message(message)

        for message in messages.menus:
            self.publish_menu_message(message)

        for message in messages.combos:
            self.publish_combo_message(message)

        for message in messages.items:
            self.publish_item_message(message)

        for message in messages.restaurant_table:
            self.publish_restaurant_table_message(message)

        for message in messages.property_videos:
            self.publish_property_video(message)
