from cataloging_service.models import SellerTypeHistory


class SellerTypeHistoryService:

    def __init__(self, seller_type_history_repository):
        self.__seller_type_history_repository = seller_type_history_repository

    def create_seller_type_history(self, property_id, from_seller_type, to_seller_type, date):
        seller_type_history_object = SellerTypeHistory()
        seller_type_history_object.property_id = property_id
        seller_type_history_object.from_seller_type = from_seller_type
        seller_type_history_object.to_seller_type = to_seller_type
        seller_type_history_object.date = date

        return self.__seller_type_history_repository.persist(seller_type_history_object)

    def get_seller_type_history_by_property(self, property_id):
        return self.__seller_type_history_repository.get_seller_type_history_by_property(property_id)

    def get_seller_type_by_date(self, property_id, date):
        seller_types = self.get_seller_type_history_by_property(property_id)
        for seller_type in seller_types:
            if date >= seller_type.date:
                return seller_type.to_seller_type.value
        raise Exception("No seller type found")
