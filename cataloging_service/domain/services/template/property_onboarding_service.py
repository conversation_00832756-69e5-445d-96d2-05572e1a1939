import logging
import hashlib
import uuid

from datetime import datetime
from typing import List, Dict, Any, Optional
from decimal import Decimal

from cataloging_service.domain import service_provider

from cataloging_service.domain.entities.properties.property_onboarding import (
    PropertyOnboardingEntity,
)
from cataloging_service.domain.entities.properties.property_profit_center import (
    PropertyProfitCenterEntity,
)
from cataloging_service.domain.entities.properties.property_department import (
    PropertyDepartmentEntity,
)
from cataloging_service.domain.entities.transactions.transaction_default_mapping import TransactionDefaultMappingEntity
from cataloging_service.domain.services.property.property_department_service import (
    PropertyDepartmentService,
)
from cataloging_service.domain.services.property.property_profit_center_service import (
    PropertyProfitCenterService,
)
from cataloging_service.domain.services.property.property_sku_service import (
    PropertySkuService,
)
from cataloging_service.domain.services.template.department_template_service import (
    DepartmentTemplateService,
)
from cataloging_service.domain.services.template.profit_center_template_service import (
    ProfitCenterTemplateService,
)
from cataloging_service.domain.services.template.sku_template_service import (
    SkuTemplateService,
)
from cataloging_service.domain.entities.properties.property_sku import (
    PropertySkuEntity,
)
from cataloging_service.domain.services.template.template_filter import TemplateFilter
from cataloging_service.domain.services.transactions import TransactionDefaultMappingService
from cataloging_service.infrastructure.repositories.transaction_default_mapping_repository import \
    TransactionDefaultMappingRepository
from cataloging_service.schemas.transaction_schemas import (
    PropertyOnboardingRequestSchema,
    PropertyOnboardingResponseSchema,
)
from cataloging_service.models import (
    TransactionMaster,
    SkuActivation,
    Param,
    SkuCategory
)
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        DepartmentTemplateService,
        ProfitCenterTemplateService,
        SkuTemplateService,
        PropertyDepartmentService,
        PropertyProfitCenterService,
        PropertySkuService,
        TransactionDefaultMappingService,
    ]
)
class PropertyOnboardingService:
    def __init__(
        self,
        department_template_service: DepartmentTemplateService,
        profit_center_template_service: ProfitCenterTemplateService,
        sku_template_service: SkuTemplateService,
        property_department_service: PropertyDepartmentService,
        property_profit_center_service: PropertyProfitCenterService,
        property_sku_service: PropertySkuService,
        transaction_default_mapping_service: TransactionDefaultMappingService,
    ):
        self.department_template_service = department_template_service
        self.profit_center_template_service = profit_center_template_service
        self.sku_template_service = sku_template_service
        self.property_department_service = property_department_service
        self.property_profit_center_service = property_profit_center_service
        self.property_sku_service = property_sku_service
        self.property_service = service_provider.property_service
        self.transaction_default_mapping_service = transaction_default_mapping_service

    def onboard_property(
        self, request: PropertyOnboardingRequestSchema
    ) -> PropertyOnboardingResponseSchema:
        onboarding_entity = PropertyOnboardingEntity(
            property_id=request.property_id,
            brand_id=request.brand_id,
            template_filters=request.custom_config,
        )

        try:
            # Step 1: Validate property
            self._validate_property(request.property_id)

            # Step 2: Create departments from templates
            if request.auto_create_departments:
                departments_created = self._create_departments_from_templates(
                    onboarding_entity, request.custom_config
                )
                onboarding_entity.departments_created = len(departments_created)

            # Step 3: Create profit centers from templates
            if request.auto_create_profit_centers:
                profit_centers_created = self._create_profit_centers_from_templates(
                    onboarding_entity, request.custom_config
                )
                onboarding_entity.profit_centers_created = len(profit_centers_created)

            # Step 4: Create SKUs from templates
            if request.auto_create_skus:
                skus_created = self._create_skus_from_templates(
                    onboarding_entity, request.custom_config
                )
                onboarding_entity.skus_created = len(skus_created)

            # Step 5: Generate transaction codes and create transactions
            if request.generate_transaction_codes:
                transaction_codes = self._generate_property_transaction_codes(onboarding_entity)
                onboarding_entity.transaction_codes_generated = len(transaction_codes)

            # Step 6: Create SKU activation records
            if request.auto_create_skus:
                activation_records = self._create_sku_activation_records(onboarding_entity)
                onboarding_entity.add_created_entity("sku_activations", activation_records)

            # Mark as completed
            onboarding_entity.mark_completed()

            # Return response
            return PropertyOnboardingResponseSchema(
                property_id=onboarding_entity.property_id,
                brand_id=onboarding_entity.brand_id,
                departments_created=onboarding_entity.departments_created,
                profit_centers_created=onboarding_entity.profit_centers_created,
                skus_created=onboarding_entity.skus_created,
                transaction_codes_generated=onboarding_entity.transaction_codes_generated,
                onboarding_status=onboarding_entity.onboarding_status,
                created_entities=onboarding_entity.created_entities,
                errors=onboarding_entity.errors,
                warnings=onboarding_entity.warnings,
                onboarded_at=onboarding_entity.onboarded_at or datetime.now(),
            )

        except Exception as e:
            onboarding_entity.add_error(f"Onboarding failed: {str(e)}")
            logger.exception(
                f"Property onboarding failed for property_id: {request.property_id}"
            )
            return PropertyOnboardingResponseSchema(
                property_id=onboarding_entity.property_id,
                brand_id=onboarding_entity.brand_id,
                departments_created=onboarding_entity.departments_created,
                profit_centers_created=onboarding_entity.profit_centers_created,
                skus_created=onboarding_entity.skus_created,
                transaction_codes_generated=onboarding_entity.transaction_codes_generated,
                onboarding_status=onboarding_entity.onboarding_status,
                created_entities=onboarding_entity.created_entities,
                errors=onboarding_entity.errors,
                warnings=onboarding_entity.warnings,
                onboarded_at=datetime.utcnow(),
            )

    def _validate_property(self, property_id: str) -> tuple:
        property_obj = self.property_service.get_property(property_id)
        if not property_obj:
            raise ValueError(f"Property with ID '{property_id}' not found")
        return property_obj

    def _create_departments_from_templates(
        self, onboarding_entity: PropertyOnboardingEntity, custom_config: Dict[str, Any]
    ) -> List[str]:
        created_departments = []
        try:
            template_filter = TemplateFilter(
                brand_id=onboarding_entity.brand_id,
                is_active=True,
                auto_create_on_property_launch=True,
            )
            department_templates = (
                self.department_template_service.get_department_templates(
                    filters=template_filter
                )
            )
            if not department_templates:
                onboarding_entity.add_warning(
                    "No department templates found for auto-creation"
                )
                return created_departments
            for template in department_templates:
                try:
                    department_entity = self._convert_department_template_to_entity(
                        template, onboarding_entity.property_id
                    )
                    created_department = (
                        self.property_department_service.create_property_department(
                            department_entity
                        )
                    )
                    created_departments.append(created_department.code)
                except Exception as e:
                    error_msg = f"Failed to create department from template {template.code}: {str(e)}"
                    onboarding_entity.add_error(error_msg)
                    logger.error(error_msg)
        except Exception as e:
            error_msg = f"Failed to create departments from templates: {str(e)}"
            onboarding_entity.add_error(error_msg)
            logger.error(error_msg)
        return created_departments

    def _create_profit_centers_from_templates(
        self, onboarding_entity: PropertyOnboardingEntity, custom_config: Dict[str, Any]
    ) -> List[str]:
        created_profit_centers = []
        try:
            template_filter = TemplateFilter(
                brand_id=onboarding_entity.brand_id,
                is_active=True,
                auto_create_on_property_launch=True,
            )
            profit_center_templates = (
                self.profit_center_template_service.get_profit_center_templates(
                    template_filter
                )
            )

            if not profit_center_templates:
                onboarding_entity.add_warning(
                    "No profit center templates found for auto-creation"
                )
                return created_profit_centers
            for template in profit_center_templates:
                try:
                    profit_center_entity = (
                        self._convert_profit_center_template_to_entity(
                            template, onboarding_entity.property_id
                        )
                    )
                    created_profit_center = self.property_profit_center_service.create_property_profit_center(
                        entity=profit_center_entity
                    )
                    created_profit_centers.append(created_profit_center.code)

                except Exception as e:
                    error_msg = f"Failed to create profit center from template {template.code}: {str(e)}"
                    onboarding_entity.add_error(error_msg)
                    logger.error(error_msg)

        except Exception as e:
            error_msg = f"Failed to create profit centers from templates: {str(e)}"
            onboarding_entity.add_error(error_msg)
            logger.error(error_msg)

        return created_profit_centers

    def _create_skus_from_templates(
        self, onboarding_entity: PropertyOnboardingEntity, custom_config: Dict[str, Any]
    ) -> List[str]:
        created_skus = []
        try:
            template_filter = TemplateFilter(
                is_active=True, auto_create_on_property_launch=True
            )

            sku_templates = self.sku_template_service.get_sku_templates(template_filter)

            brand_sku_templates = []
            for template in sku_templates:
                dept_filter = TemplateFilter(
                    brand_id=onboarding_entity.brand_id,
                    code=template.department_template_code,
                    is_active=True,
                )
                dept_templates = (
                    self.department_template_service.get_department_templates(
                        dept_filter
                    )
                )
                if dept_templates:
                    brand_sku_templates.append(template)
            if not brand_sku_templates:
                onboarding_entity.add_warning(
                    "No SKU templates found for auto-creation"
                )
                return created_skus
            for template in brand_sku_templates:
                try:
                    sku_entity = self._convert_sku_template_to_entity(
                        template, onboarding_entity.property_id
                    )
                    created_sku = self.property_sku_service.create_property_sku(
                        entity=sku_entity
                    )
                    created_skus.append(created_sku.code)

                except Exception as e:
                    error_msg = (
                        f"Failed to create SKU from template {template.code}: {str(e)}"
                    )
                    onboarding_entity.add_error(error_msg)
                    logger.error(error_msg)

        except Exception as e:
            error_msg = f"Failed to create SKUs from templates: {str(e)}"
            onboarding_entity.add_error(error_msg)
            logger.error(error_msg)

        return created_skus

    def _generate_property_transaction_codes(
        self, onboarding_entity: PropertyOnboardingEntity
    ) -> List[str]:
        transaction_codes = []
        try:
            created_skus = onboarding_entity.created_entities.get("skus", [])

            for sku_code in created_skus:
                tax_info = self._get_tax_information_for_sku(sku_code, onboarding_entity.property_id)

                # Get tenant transaction template
                tenant_template = self._get_tenant_transaction_template("TENANT_FOOD_BURGER_SALE")

                # Create main property transaction
                main_tx_code = self._create_property_transaction_main(
                    onboarding_entity, sku_code, tenant_template
                )
                transaction_codes.append(main_tx_code)
                logger.info(f"Transaction Created: {main_tx_code}")

                # Create tax transactions (CGST: 2.5%, SGST: 2.5%)
                cgst_tx_code = self._create_property_transaction_tax_cgst(
                    onboarding_entity, sku_code, tax_info
                )
                transaction_codes.append(cgst_tx_code)
                logger.info(f"Tax Transaction Created (CGST): {cgst_tx_code}")

                sgst_tx_code = self._create_property_transaction_tax_sgst(
                    onboarding_entity, sku_code, tax_info
                )
                transaction_codes.append(sgst_tx_code)
                logger.info(f"Tax Transaction Created (SGST): {sgst_tx_code}")

                # Create allowance transaction
                allowance_tx_code = self._create_property_transaction_allowance(
                    onboarding_entity, sku_code
                )
                transaction_codes.append(allowance_tx_code)
                logger.info(f"Allowance Transaction Created: {allowance_tx_code}")

                # Create payment transaction
                payment_tx_codes = self._create_payment_transactions(
                    onboarding_entity, sku_code
                )
                transaction_codes.extend(payment_tx_codes)
                logger.info(f"Payment Transactions Created: {len(payment_tx_codes)} transactions")

            logger.info("All Transaction Codes Generated")

            if not transaction_codes:
                onboarding_entity.add_warning(
                    "No transaction codes generated - no SKUs were created"
                )

        except Exception as e:
            error_msg = f"Failed to generate property transaction codes: {str(e)}"
            onboarding_entity.add_error(error_msg)
            logger.error(error_msg)

        return transaction_codes

    def _get_tax_information_for_sku(self, sku_code: str, property_id: str) -> Dict[str, Any]:
        """Get Tax Information (SKU Category) with tax components"""
        try:
            # Get SKU details
            property_skus = self.property_sku_service.get_property_skus(property_id)
            sku = next((s for s in property_skus if s.code == sku_code), None)

            if not sku:
                raise ValueError(f"SKU {sku_code} not found for property {property_id}")

            # Get SKU category for tax information
            sku_category = service_provider.db_session.query(SkuCategory).filter_by(
                id=sku.sku_category_id
            ).first()

            if not sku_category:
                raise ValueError(f"SKU category not found for SKU {sku_code}")

            # Tax Components [CGST: 2.5%, SGST: 2.5%]
            tax_info = {
                "sku_category_id": sku_category.id,
                "sku_category_name": sku_category.name,
                "hsn_sac": sku_category.hsn_sac,
                "tax_components": {
                    "CGST": Decimal("2.5"),  # 2.5%
                    "SGST": Decimal("2.5"),  # 2.5%
                    "total_tax": Decimal("5.0")  # Total 5%
                }
            }

            logger.info(f"Tax information retrieved for SKU {sku_code}: {tax_info}")
            return tax_info

        except Exception as e:
            logger.error(f"Failed to get tax information for SKU {sku_code}: {str(e)}")
            return {
                "sku_category_id": None,
                "sku_category_name": "Unknown",
                "hsn_sac": None,
                "tax_components": {
                    "CGST": Decimal("2.5"),
                    "SGST": Decimal("2.5"),
                    "total_tax": Decimal("5.0")
                }
            }

    def _get_tenant_transaction_template(self, template_code: str) -> Optional[TransactionDefaultMappingEntity]:
        return self.transaction_default_mapping_service.get_transaction_default_mapping(template_code=template_code)

    def _create_property_transaction_main(
        self, onboarding_entity: PropertyOnboardingEntity, sku_code: str, template: Dict[str, Any]
    ) -> str:
        """Create Property Transaction (Main)"""
        try:
            transaction_code = f"TXN_MAIN_{onboarding_entity.property_id}_{sku_code}_{datetime.now().strftime('%Y%m%d%H%M%S')}"

            transaction = TransactionMaster(
                transaction_code=transaction_code,
                name=f"Main Transaction - {sku_code}",
                property_id=onboarding_entity.property_id,
                entity_type=template.get("entity_type", "HOTEL"),
                transaction_type=template.get("transaction_type", "SKU_SALE"),
                transaction_id=sku_code,
                operational_unit_id=onboarding_entity.property_id,
                operational_unit_type="PROPERTY",
                source="PROPERTY_ONBOARDING",
                gl_code=template.get("default_gl_code"),
                erp_id=template.get("default_erp_id"),
                is_merge=template.get("default_is_merge", False),
                particulars=template.get("default_particulars", f"Main transaction for {sku_code}"),
                status="ACTIVE",
                transaction_details={
                    "sku_code": sku_code,
                    "template_code": template.get("template_code"),
                    "created_during_onboarding": True,
                    **template.get("transaction_details", {})
                }
            )

            service_provider.db_session.add(transaction)
            service_provider.db_session.commit()

            return transaction_code

        except Exception as e:
            service_provider.db_session.rollback()
            logger.error(f"Failed to create main transaction for {sku_code}: {str(e)}")
            raise

    def _create_property_transaction_tax_cgst(
        self, onboarding_entity: PropertyOnboardingEntity, sku_code: str, tax_info: Dict[str, Any]
    ) -> str:
        """Create Property Transaction (Tax CGST)"""
        try:
            transaction_code = f"TXN_CGST_{onboarding_entity.property_id}_{sku_code}_{datetime.now().strftime('%Y%m%d%H%M%S')}"

            transaction = TransactionMaster(
                transaction_code=transaction_code,
                name=f"CGST Tax - {sku_code}",
                property_id=onboarding_entity.property_id,
                entity_type="HOTEL",
                transaction_type="CGST",
                transaction_id=sku_code,
                operational_unit_id=onboarding_entity.property_id,
                operational_unit_type="PROPERTY",
                source="PROPERTY_ONBOARDING",
                gl_code="2301",  # Standard CGST GL code
                erp_id=None,
                is_merge=False,
                particulars=f"CGST 2.5% for {sku_code}",
                status="ACTIVE",
                transaction_details={
                    "sku_code": sku_code,
                    "tax_type": "CGST",
                    "tax_rate": str(tax_info["tax_components"]["CGST"]),
                    "hsn_sac": tax_info.get("hsn_sac"),
                    "sku_category_id": tax_info.get("sku_category_id"),
                    "created_during_onboarding": True
                }
            )

            service_provider.db_session.add(transaction)
            service_provider.db_session.commit()

            return transaction_code

        except Exception as e:
            service_provider.db_session.rollback()
            logger.error(f"Failed to create CGST transaction for {sku_code}: {str(e)}")
            raise

    def _convert_department_template_to_entity(self, template, property_id: str):
        return PropertyDepartmentEntity(
            property_id=property_id,
            code=template.code,
            name=template.name,
            parent_id=None,
            description=template.description,
            financial_code=template.financial_code,
            is_active=template.is_active,
            is_custom=False,
            created_from_template_code=template.code,
        )

    def _create_property_transaction_tax_sgst(
        self, onboarding_entity: PropertyOnboardingEntity, sku_code: str, tax_info: Dict[str, Any]
    ) -> str:
        """Create Property Transaction (Tax SGST)"""
        try:
            transaction_code = f"TXN_SGST_{onboarding_entity.property_id}_{sku_code}_{datetime.now().strftime('%Y%m%d%H%M%S')}"

            transaction = TransactionMaster(
                transaction_code=transaction_code,
                name=f"SGST Tax - {sku_code}",
                property_id=onboarding_entity.property_id,
                entity_type="HOTEL",
                transaction_type="SGST",
                transaction_id=sku_code,
                operational_unit_id=onboarding_entity.property_id,
                operational_unit_type="PROPERTY",
                source="PROPERTY_ONBOARDING",
                gl_code="2302",  # Standard SGST GL code
                erp_id=None,
                is_merge=False,
                particulars=f"SGST 2.5% for {sku_code}",
                status="ACTIVE",
                transaction_details={
                    "sku_code": sku_code,
                    "tax_type": "SGST",
                    "tax_rate": str(tax_info["tax_components"]["SGST"]),
                    "hsn_sac": tax_info.get("hsn_sac"),
                    "sku_category_id": tax_info.get("sku_category_id"),
                    "created_during_onboarding": True
                }
            )

            service_provider.db_session.add(transaction)
            service_provider.db_session.commit()

            return transaction_code

        except Exception as e:
            service_provider.db_session.rollback()
            logger.error(f"Failed to create SGST transaction for {sku_code}: {str(e)}")
            raise

    def _create_property_transaction_allowance(
        self, onboarding_entity: PropertyOnboardingEntity, sku_code: str
    ) -> str:
        """Create Property Transaction (Allowance)"""
        try:
            transaction_code = f"TXN_ALLOW_{onboarding_entity.property_id}_{sku_code}_{datetime.now().strftime('%Y%m%d%H%M%S')}"

            transaction = TransactionMaster(
                transaction_code=transaction_code,
                name=f"Allowance - {sku_code}",
                property_id=onboarding_entity.property_id,
                entity_type="HOTEL",
                transaction_type="SKU_ALLOWANCE",
                transaction_id=sku_code,
                operational_unit_id=onboarding_entity.property_id,
                operational_unit_type="PROPERTY",
                source="PROPERTY_ONBOARDING",
                gl_code="5001",  # Standard allowance GL code
                erp_id=None,
                is_merge=False,
                particulars=f"Allowance transaction for {sku_code}",
                status="ACTIVE",
                transaction_details={
                    "sku_code": sku_code,
                    "allowance_type": "STANDARD",
                    "created_during_onboarding": True
                }
            )

            service_provider.db_session.add(transaction)
            service_provider.db_session.commit()

            return transaction_code

        except Exception as e:
            service_provider.db_session.rollback()
            logger.error(f"Failed to create allowance transaction for {sku_code}: {str(e)}")
            raise

    def _create_payment_transactions(
        self, onboarding_entity: PropertyOnboardingEntity, sku_code: str
    ) -> List[str]:
        """Create Payment Transactions"""
        payment_codes = []
        try:
            # Create payment transactions for common payment methods
            payment_methods = ["CASH", "CARD", "UPI"]

            for method in payment_methods:
                transaction_code = f"TXN_PAY_{method}_{onboarding_entity.property_id}_{sku_code}_{datetime.now().strftime('%Y%m%d%H%M%S')}"

                transaction = TransactionMaster(
                    transaction_code=transaction_code,
                    name=f"Payment {method} - {sku_code}",
                    property_id=onboarding_entity.property_id,
                    entity_type="HOTEL",
                    transaction_type="PAYMENT",
                    transaction_id=f"{sku_code}_{method}",
                    operational_unit_id=onboarding_entity.property_id,
                    operational_unit_type="PROPERTY",
                    source="PROPERTY_ONBOARDING",
                    gl_code=self._get_payment_gl_code(method),
                    erp_id=None,
                    is_merge=False,
                    particulars=f"Payment via {method} for {sku_code}",
                    status="ACTIVE",
                    transaction_details={
                        "sku_code": sku_code,
                        "payment_method": method,
                        "created_during_onboarding": True
                    }
                )

                service_provider.db_session.add(transaction)
                payment_codes.append(transaction_code)

            service_provider.db_session.commit()
            logger.info(f"Payment Transactions Created: {len(payment_codes)} transactions")

            return payment_codes

        except Exception as e:
            service_provider.db_session.rollback()
            logger.error(f"Failed to create payment transactions for {sku_code}: {str(e)}")
            raise

    def _get_payment_gl_code(self, payment_method: str) -> str:
        """Get GL code for payment method"""
        gl_codes = {
            "CASH": "1001",
            "CARD": "1002",
            "UPI": "1003"
        }
        return gl_codes.get(payment_method, "1000")

    def _create_sku_activation_records(self, onboarding_entity: PropertyOnboardingEntity) -> List[str]:
        """Create SKU Activation Records"""
        activation_records = []
        try:
            # Get Required Services (from param) - [POS, INVENTORY, REPORTING]
            required_services = self._get_required_services()
            created_skus = onboarding_entity.created_entities.get("skus", [])

            for sku_code in created_skus:
                # Get SKU ID
                property_skus = self.property_sku_service.get_property_skus(onboarding_entity.property_id)
                sku = next((s for s in property_skus if s.code == sku_code), None)

                if not sku:
                    logger.warning(f"SKU {sku_code} not found for activation")
                    continue

                for service in required_services:
                    try:
                        activation = SkuActivation(
                            property_id=onboarding_entity.property_id,
                            sku_id=sku.id,
                            service_id=service["id"]
                        )

                        service_provider.db_session.add(activation)
                        activation_record_id = f"{onboarding_entity.property_id}_{sku.id}_{service['id']}"
                        activation_records.append(activation_record_id)

                        logger.info(f"Activation Record Created: {activation_record_id}")

                    except Exception as e:
                        logger.error(f"Failed to create activation record for {sku_code} - {service['name']}: {str(e)}")
                        continue

            service_provider.db_session.commit()
            logger.info(f"SKU Activation Records Created: {len(activation_records)} records")

            return activation_records

        except Exception as e:
            service_provider.db_session.rollback()
            logger.error(f"Failed to create SKU activation records: {str(e)}")
            return []

    def _get_required_services(self) -> List[Dict[str, Any]]:
        """Get Required Services (from param) - [POS, INVENTORY, REPORTING]"""
        try:
            service_names = ["POS", "INVENTORY", "REPORTING"]
            services = []

            for service_name in service_names:
                param = service_provider.db_session.query(Param).filter_by(
                    value=service_name,
                    is_active=True
                ).first()

                if param:
                    services.append({
                        "id": param.id,
                        "name": service_name,
                        "value": param.value
                    })
                else:
                    logger.warning(f"Service parameter not found: {service_name}")

            logger.info(f"Required services retrieved: {[s['name'] for s in services]}")
            return services

        except Exception as e:
            logger.error(f"Failed to get required services: {str(e)}")
            return []

    def _convert_profit_center_template_to_entity(self, template, property_id: str):
        profit_center_id = self._generate_profit_center_id(property_id, template.code)
        department_id = self._resolve_department_id_from_template_code(
            property_id, template.department_template_code
        )
        if not department_id:
            raise ValueError(
                f"Could not resolve department_id for template code '{template.department_template_code}' "
                f"in property '{property_id}'. Ensure the department was created first."
            )

        return PropertyProfitCenterEntity(
            id=profit_center_id,
            property_id=property_id,
            code=template.code,
            name=template.name,
            description=template.description,
            department_id=department_id,
            is_active=template.is_active,
            is_auto_created=True,
            is_custom=False,
            created_from_template_code=template.code,
            config=template.template_config or {},
        )

    def _convert_sku_template_to_entity(self, template, property_id: str):
        department_id = self._resolve_department_id_from_template_code(
            property_id, template.department_template_code
        )
        profit_center_id = self._resolve_profit_center_id_from_template_code(
            property_id, template.profit_center_template_code
        )
        return PropertySkuEntity(
            property_id=property_id,
            code=template.code,
            name=template.name,
            display_name=template.display_name,
            description=template.description,
            department_id=department_id,
            profit_center_id=profit_center_id,
            sku_category_id=template.category_id,
            default_list_price=template.default_list_price,
            default_sale_price=template.default_sale_price,
            hsn_sac=template.hsn_sac,
            tax_at_room_rate=template.tax_at_room_rate,
            is_active=template.is_active,
            is_custom=False,
            is_modular=template.is_modular,
            saleable=template.saleable,
            chargeable_per_occupant=template.chargeable_per_occupant,
            is_property_inclusion=template.is_property_inclusion,
            created_from_template_code=template.code,
            config=template.template_config or {},
            sku_details=template.sku_details or {},
            sku_count=0,
            flat_count_for_creation=0,
            offering={},
            frequency={},
        )

    def _resolve_department_id_from_template_code(
        self, property_id: str, template_code: str
    ) -> Optional[int]:
        try:
            departments = self.property_department_service.get_property_departments(
                property_id, active_only=False
            )
            for dept in departments:
                if dept.created_from_template_code == template_code:
                    return dept.id
            logger.warning(
                f"Department with template code '{template_code}' not found for property {property_id}. "
                f"Available departments: {[d.created_from_template_code for d in departments if d.created_from_template_code]}"
            )
            return None

        except Exception as e:
            logger.error(
                f"Failed to resolve department ID for template code {template_code}: {e}"
            )
            return None

    def _resolve_profit_center_id_from_template_code(
        self, property_id: str, template_code: str
    ) -> Optional[str]:
        try:
            profit_centers = (
                self.property_profit_center_service.get_property_profit_centers(
                    property_id, active_only=False
                )
            )
            for pc in profit_centers:
                if pc.created_from_template_code == template_code:
                    return pc.id
            raise ValueError(
                f"Profit center with template code '{template_code}' not found for property {property_id}"
            )

        except Exception as e:
            logger.error(
                f"Failed to resolve profit center ID for template code {template_code}: {e}"
            )
            return None

    def _generate_profit_center_id(self, property_id: str, code: str) -> str:
        base_string = f"{property_id}_{code}_{datetime.now().isoformat()}"
        hash_object = hashlib.md5(base_string.encode())
        short_hash = hash_object.hexdigest()[:8].upper()
        profit_center_id = f"PC_{property_id}_{short_hash}"
        return profit_center_id
