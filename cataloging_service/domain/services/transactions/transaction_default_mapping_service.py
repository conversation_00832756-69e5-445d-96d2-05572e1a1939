"""
Transaction Default Mapping Service for handling transaction default mappings and related business logic.
"""

from typing import List, Optional

from cataloging_service.domain.entities.transactions.transaction_default_mapping import TransactionDefaultMappingEntity
from cataloging_service.infrastructure.repositories.transaction_default_mapping_repository import \
    TransactionDefaultMappingRepository
from object_registry import register_instance



@register_instance(
    dependencies=[TransactionDefaultMappingRepository]
)
class TransactionDefaultMappingService:
    def __init__(self, transaction_default_mapping_repository):
        self.transaction_default_mapping_repository = transaction_default_mapping_repository
    def get_transaction_default_mapping(self, brand_id: int, template_code: str) -> Optional[TransactionDefaultMappingEntity]:
        return self.transaction_default_mapping_repository.get_transaction_default_mapping(transaction_type, transaction_type_code, entity_type, brand_id)
