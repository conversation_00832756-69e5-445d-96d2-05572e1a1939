class Messages:
    def __init__(self):
        self.properties = list()
        self.rooms = list()
        self.room_configs = list()
        self.restaurants = list()
        self.bars = list()
        self.halls = list()
        self.property_amenities = list()
        self.room_amenities = list()
        self.property_images = list()
        self.amenity_summaries = list()
        self.sku_categories = list()
        self.channels = list()
        self.sub_channels = list()
        self.applications = list()
        self.room_types = list()
        self.cities = list()
        self.skus = list()
        self.pricing_policies = list()
        self.property_skus = list()
        self.sellers = list()
        self.seller_sku = list()
        self.ruptub_details = list()
        self.menus = list()
        self.combos = list()
        self.items = list()
        self.restaurant_table = list()
        self.property_videos = list()

    def purge(self):
        self.properties.clear()
        self.rooms.clear()
        self.room_configs.clear()
        self.restaurants.clear()
        self.bars.clear()
        self.halls.clear()
        self.property_amenities.clear()
        self.room_amenities.clear()
        self.property_images.clear()
        self.amenity_summaries.clear()
        self.sku_categories.clear()
        self.channels.clear()
        self.sub_channels.clear()
        self.applications.clear()
        self.room_types.clear()
        self.cities.clear()
        self.skus.clear()
        self.pricing_policies.clear()
        self.property_skus.clear()
        self.sellers.clear()
        self.seller_sku.clear()
        self.ruptub_details.clear()
        self.menus.clear()
        self.combos.clear()
        self.items.clear()
        self.restaurant_table.clear()
        self.property_videos.clear()

    def insert_property_message(self, message):
        self.properties.append(message)

    def insert_room_messages(self, message):
        self.rooms.append(message)

    def insert_room_config_messages(self, message):
        self.room_configs.append(message)

    def insert_restaurant_messages(self, message):
        self.restaurants.append(message)

    def insert_bar_messages(self, message):
        self.bars.append(message)

    def insert_hall_messages(self, message):
        self.halls.append(message)

    def insert_property_amenities(self, message):
        self.property_amenities.append(message)

    def insert_room_amenities(self, message):
        self.room_amenities.append(message)

    def insert_property_images(self, message):
        self.property_images.append(message)

    def insert_amenity_summaries(self, amenity_summary):
        self.amenity_summaries.append(amenity_summary)

    def insert_sku_categories(self, message):
        self.sku_categories.append(message)

    def insert_channels(self, message):
        self.channels.append(message)

    def insert_sub_channels(self, message):
        self.sub_channels.append(message)

    def insert_applications(self, message):
        self.applications.append(message)

    def insert_room_types(self, message):
        self.room_types.append(message)

    def insert_city(self, message):
        self.cities.append(message)

    def insert_sku(self, message):
        self.skus.append(message)

    def insert_pricing_policy(self, message):
        self.pricing_policies.append(message)

    def insert_property_sku(self, message):
        self.property_skus.append(message)

    def insert_seller(self, message):
        self.sellers.append(message)

    def insert_seller_sku(self, message):
        self.seller_sku.append(message)

    def insert_ruptub_details(self, message):
        self.ruptub_details.append(message)

    def insert_menu(self, message):
        self.menus.append(message)

    def insert_combo(self, message):
        self.combos.append(message)

    def insert_item(self, message):
        self.items.append(message)

    def insert_restaurant_table(self, message):
        self.restaurant_table.append(message)

    def insert_property_video(self, message):
        self.property_videos.append(message)
