import json
from decimal import Decimal

from cataloging_service.api.response_schema import SellerResponseSchema, SellerSkuResponseSchema
from cataloging_service.api.schemas import PropertySchema, RoomSchema, RoomTypeConfigurationSchema, RestaurantSchema, \
    BarSchema, BanquetHallSchema, PropertyAmenitySchema, RoomAmenitySchema, PropertyImageSchema, SkuCategorySchema, \
    ChannelSchema, SubChannelSchema, ApplicationSchema, RoomTypeSchema, CitySchema, SkuSchema, PricingPolicySchema, \
    PropertySkuSchema, RuptubLegalEntityDetailsSchema, RoomRackRateSchema, MenuMessageSchema, ComboMessageSchema, \
    ItemMessageSchema, RestaurantTableShortSchema, PropertyVideoSchema
from cataloging_service.constants import messaging_constants


def custom_json_encoder(val):
    if isinstance(val, Decimal):
        return str(val)

    raise TypeError()


def create_wrapper_json(operation, entity, property_id, hx_id, data, room=None, action=None):
    json_map = {'operation_type': operation, 'entity': entity,
                'cs_property_id': property_id, 'hx_id': hx_id, 'data': data}
    if action:
        json_map['action'] = action
    if room:
        json_map['room_id'] = room.id

    return json.loads(json.dumps(json_map, default=custom_json_encoder))


class PropertyMessagingWrapper:
    def __init__(self, property_object, created, deleted, action):
        if created:
            self.operation = messaging_constants.OPERATION_TYPE_CREATE
        elif deleted:
            self.operation = messaging_constants.OPERATION_TYPE_DELETE
        else:
            self.operation = messaging_constants.OPERATION_TYPE_UPDATE

        self.property_object = property_object
        self.action = action

    def get_json(self):
        data = PropertySchema().dump(self.property_object).data
        return create_wrapper_json(self.operation, messaging_constants.PROPERTY_ENTITY_TYPE, self.property_object.id,
                                   self.property_object.hx_id, data, action=self.action)


class RoomMessagingWrapper:
    def __init__(self, room_object, created, deleted):
        if created:
            self.operation = messaging_constants.OPERATION_TYPE_CREATE
        elif deleted:
            self.operation = messaging_constants.OPERATION_TYPE_DELETE
        else:
            self.operation = messaging_constants.OPERATION_TYPE_UPDATE

        self.room_object = room_object

    def get_json(self):
        data = RoomSchema().dump(self.room_object).data

        return create_wrapper_json(self.operation, messaging_constants.ROOM_ENTITY_TYPE, self.room_object.property_id,
                                   self.room_object.property.hx_id, data)


class RoomRackRateMessagingWrapper:
    def __init__(self, room_rack_rate_object, created, deleted):
        if created:
            self.operation = messaging_constants.OPERATION_TYPE_CREATE
        elif deleted:
            self.operation = messaging_constants.OPERATION_TYPE_DELETE
        else:
            self.operation = messaging_constants.OPERATION_TYPE_UPDATE

        self.room_rack_rate_object = room_rack_rate_object

    def get_json(self):
        data = RoomRackRateSchema().dump(self.room_rack_rate_object).data

        return create_wrapper_json(
            self.operation,
            messaging_constants.ROOM_RACK_RATE_ENTITY_TYPE,
            self.room_rack_rate_object.property_id,
            self.room_rack_rate_object.property.hx_id,
            data
        )


class RoomTypeConfigMessagingWrapper:
    def __init__(self, room_type_config_object, created, deleted):
        if created:
            self.operation = messaging_constants.OPERATION_TYPE_CREATE
        elif deleted:
            self.operation = messaging_constants.OPERATION_TYPE_DELETE
        else:
            self.operation = messaging_constants.OPERATION_TYPE_UPDATE

        self.room_type_config_object = room_type_config_object

    def get_json(self):
        data = RoomTypeConfigurationSchema().dump(self.room_type_config_object).data

        return create_wrapper_json(self.operation, messaging_constants.ROOM_TYPE_CONFIG_ENTITY_TYPE,
                                   self.room_type_config_object.property_id,
                                   self.room_type_config_object.property.hx_id, data)


class RestaurantMessagingWrapper:
    def __init__(self, restaurant, created, deleted):
        if created:
            self.operation = messaging_constants.OPERATION_TYPE_CREATE
        elif deleted:
            self.operation = messaging_constants.OPERATION_TYPE_DELETE
        else:
            self.operation = messaging_constants.OPERATION_TYPE_UPDATE

        self.restaurant = restaurant

    def get_json(self):
        data = RestaurantSchema().dump(self.restaurant).data

        return create_wrapper_json(self.operation, messaging_constants.RESTAURANT_ENTITY_TYPE,
                                   self.restaurant.property_id,
                                   self.restaurant.property.hx_id, data)


class RestaurantTableMessagingWrapper:
    def __init__(self, restaurant_table, seller_id, created, deleted):
        if created:
            self.operation = messaging_constants.OPERATION_TYPE_CREATE
        elif deleted:
            self.operation = messaging_constants.OPERATION_TYPE_DELETE
        else:
            self.operation = messaging_constants.OPERATION_TYPE_UPDATE

        self.restaurant_table = restaurant_table
        self.restaurant_table.seller_id = seller_id

    def get_json(self):
        data = RestaurantTableShortSchema().dump(self.restaurant_table).data

        json_map = {'operation_type': self.operation, 'entity': messaging_constants.RESTAURANT_TABLE_ENTITY_TYPE,
                    'data': data}
        return json.loads(json.dumps(json_map, default=custom_json_encoder))


class BarMessagingWrapper:
    def __init__(self, bar, created, deleted):
        if created:
            self.operation = messaging_constants.OPERATION_TYPE_CREATE
        elif deleted:
            self.operation = messaging_constants.OPERATION_TYPE_DELETE
        else:
            self.operation = messaging_constants.OPERATION_TYPE_UPDATE

        self.bar = bar

    def get_json(self):
        data = BarSchema().dump(self.bar).data

        return create_wrapper_json(self.operation, messaging_constants.BAR_ENTITY_TYPE,
                                   self.bar.property_id,
                                   self.bar.property.hx_id, data)


class BanquetHallWrapper:
    def __init__(self, hall, created, deleted):
        if created:
            self.operation = messaging_constants.OPERATION_TYPE_CREATE
        elif deleted:
            self.operation = messaging_constants.OPERATION_TYPE_DELETE
        else:
            self.operation = messaging_constants.OPERATION_TYPE_UPDATE

        self.hall = hall

    def get_json(self):
        data = BanquetHallSchema().dump(self.hall).data

        return create_wrapper_json(self.operation, messaging_constants.HALL_ENTITY_TYPE,
                                   self.hall.property_id,
                                   self.hall.property.hx_id, data)


class PropertyAmenitiesWrapper:
    def __init__(self, amenities, created, deleted):
        if created:
            self.operation = messaging_constants.OPERATION_TYPE_CREATE
        elif deleted:
            self.operation = messaging_constants.OPERATION_TYPE_DELETE
        else:
            self.operation = messaging_constants.OPERATION_TYPE_UPDATE

        self.amenities = amenities

    def get_json(self):
        data = PropertyAmenitySchema().dump(self.amenities).data

        return create_wrapper_json(self.operation, messaging_constants.PROPERTY_AMENITY_ENTITY_TYPE,
                                   self.amenities.property_id,
                                   self.amenities.property.hx_id, data)


class PropertyImageWrapper:
    def __init__(self, property_image, created, deleted):
        if created:
            self.operation = messaging_constants.OPERATION_TYPE_CREATE
        elif deleted:
            self.operation = messaging_constants.OPERATION_TYPE_DELETE
        else:
            self.operation = messaging_constants.OPERATION_TYPE_UPDATE

        self.property_image = property_image

    def get_json(self):
        data = PropertyImageSchema().dump(self.property_image).data

        return create_wrapper_json(self.operation, messaging_constants.PROPERTY_IMAGE_ENTITY_TYPE,
                                   self.property_image.property.id,
                                   self.property_image.property.hx_id, data)


class AmenitySummaryWrapper:
    def __init__(self, amenity_summary, created, deleted):
        if created:
            self.operation = messaging_constants.OPERATION_TYPE_CREATE
        elif deleted:
            self.operation = messaging_constants.OPERATION_TYPE_DELETE
        else:
            self.operation = messaging_constants.OPERATION_TYPE_UPDATE

        self.amenity_summary = amenity_summary

    def get_json(self):
        data = self.amenity_summary.summary

        return create_wrapper_json(self.operation, messaging_constants.AMENITY_SUMMARY_ENTITY_TYPE,
                                   self.amenity_summary.property.id,
                                   self.amenity_summary.property.hx_id, data)


class RoomAmenitiesWrapper:
    def __init__(self, amenities, created, deleted):
        if created:
            self.operation = messaging_constants.OPERATION_TYPE_CREATE
        elif deleted:
            self.operation = messaging_constants.OPERATION_TYPE_DELETE
        else:
            self.operation = messaging_constants.OPERATION_TYPE_UPDATE

        self.amenities = amenities

    def get_json(self):
        data = RoomAmenitySchema().dump(self.amenities).data

        return create_wrapper_json(self.operation, messaging_constants.ROOM_AMENITY_ENTITY_TYPE,
                                   self.amenities.room.property.id,
                                   self.amenities.room.property.hx_id, data, self.amenities.room)


class SkuCategoryWrapper:
    def __init__(self, categories, created, deleted):
        if created:
            self.operation = messaging_constants.OPERATION_TYPE_CREATE
        elif deleted:
            self.operation = messaging_constants.OPERATION_TYPE_DELETE
        else:
            self.operation = messaging_constants.OPERATION_TYPE_UPDATE

        self.categories = categories

    def get_json(self):
        data = SkuCategorySchema().dump(self.categories).data

        json_map = {'operation_type': self.operation, 'entity': messaging_constants.SKU_CATEGORY_ENTITY,
                    'data': data}
        return json.loads(json.dumps(json_map, default=custom_json_encoder))


class ChannelWrapper:
    def __init__(self, channels, created, deleted):
        if created:
            self.operation = messaging_constants.OPERATION_TYPE_CREATE
        elif deleted:
            self.operation = messaging_constants.OPERATION_TYPE_DELETE
        else:
            self.operation = messaging_constants.OPERATION_TYPE_UPDATE

        self.channels = channels

    def get_json(self):
        data = ChannelSchema().dump(self.channels).data

        json_map = {'operation_type': self.operation, 'entity': messaging_constants.CHANNEL_ENTITY,
                    'data': data}
        return json.loads(json.dumps(json_map, default=custom_json_encoder))


class SubChannelWrapper:
    def __init__(self, sub_channels, created, deleted):
        if created:
            self.operation = messaging_constants.OPERATION_TYPE_CREATE
        elif deleted:
            self.operation = messaging_constants.OPERATION_TYPE_DELETE
        else:
            self.operation = messaging_constants.OPERATION_TYPE_UPDATE

        self.sub_channels = sub_channels

    def get_json(self):
        data = SubChannelSchema().dump(self.sub_channels).data

        json_map = {'operation_type': self.operation, 'entity': messaging_constants.SUB_CHANNEL_ENTITY,
                    'data': data}
        return json.loads(json.dumps(json_map, default=custom_json_encoder))


class ApplicationWrapper:
    def __init__(self, application, created, deleted):
        if created:
            self.operation = messaging_constants.OPERATION_TYPE_CREATE
        elif deleted:
            self.operation = messaging_constants.OPERATION_TYPE_DELETE
        else:
            self.operation = messaging_constants.OPERATION_TYPE_UPDATE

        self.application = application

    def get_json(self):
        data = ApplicationSchema().dump(self.application).data

        json_map = {'operation_type': self.operation, 'entity': messaging_constants.APPLICATION_ENTITY,
                    'data': data}
        return json.loads(json.dumps(json_map, default=custom_json_encoder))


class RoomTypeWrapper:
    def __init__(self, room_type, created, deleted):
        if created:
            self.operation = messaging_constants.OPERATION_TYPE_CREATE
        elif deleted:
            self.operation = messaging_constants.OPERATION_TYPE_DELETE
        else:
            self.operation = messaging_constants.OPERATION_TYPE_UPDATE

        self.room_type = room_type

    def get_json(self):
        data = RoomTypeSchema().dump(self.room_type).data

        json_map = {'operation_type': self.operation, 'entity': messaging_constants.ROOM_TYPE_ENTITY,
                    'data': data}
        return json.loads(json.dumps(json_map, default=custom_json_encoder))


class CityWrapper:
    def __init__(self, city, created, deleted):
        if created:
            self.operation = messaging_constants.OPERATION_TYPE_CREATE
        elif deleted:
            self.operation = messaging_constants.OPERATION_TYPE_DELETE
        else:
            self.operation = messaging_constants.OPERATION_TYPE_UPDATE

        self.city = city

    def get_json(self):
        data = CitySchema().dump(self.city).data

        json_map = {'operation_type': self.operation, 'entity': messaging_constants.CITY_ENTITY,
                    'data': data}
        return json.loads(json.dumps(json_map, default=custom_json_encoder))


class SkuWrapper:
    def __init__(self, sku_s, created, deleted):
        if created:
            self.operation = messaging_constants.OPERATION_TYPE_CREATE
        elif deleted:
            self.operation = messaging_constants.OPERATION_TYPE_DELETE
        else:
            self.operation = messaging_constants.OPERATION_TYPE_UPDATE

        self.sku_s = sku_s

    def get_json(self):
        data = SkuSchema().dump(self.sku_s).data

        json_map = {'operation_type': self.operation, 'entity': messaging_constants.SKU_ENTITY,
                    'data': data}
        return json.loads(json.dumps(json_map, default=custom_json_encoder))


class PricingPolicyWrapper:
    def __init__(self, policies, created, deleted):
        if created:
            self.operation = messaging_constants.OPERATION_TYPE_CREATE
        elif deleted:
            self.operation = messaging_constants.OPERATION_TYPE_DELETE
        else:
            self.operation = messaging_constants.OPERATION_TYPE_UPDATE

        self.policies = policies

    def get_json(self):
        data = PricingPolicySchema().dump(self.policies).data

        json_map = {'operation_type': self.operation, 'entity': messaging_constants.PRICING_POLICY_ENTITY, 'data': data}
        return json.loads(json.dumps(json_map, default=custom_json_encoder))


class PropertySkuWrapper:
    def __init__(self, property_skus, created, deleted):
        if created:
            self.operation = messaging_constants.OPERATION_TYPE_CREATE
        elif deleted:
            self.operation = messaging_constants.OPERATION_TYPE_DELETE
        else:
            self.operation = messaging_constants.OPERATION_TYPE_UPDATE

        self.property_skus = property_skus

    def get_json(self):
        data = PropertySkuSchema().dump(self.property_skus).data

        json_map = {'operation_type': self.operation, 'entity': messaging_constants.PROPERTY_SKU_ENTITY, 'data': data}
        return json.loads(json.dumps(json_map, default=custom_json_encoder))


class SkuUnderPropertyWrapper:
    def __init__(self, property_skus, created, deleted):
        if created:
            self.operation = messaging_constants.OPERATION_TYPE_CREATE
        elif deleted:
            self.operation = messaging_constants.OPERATION_TYPE_DELETE
        else:
            self.operation = messaging_constants.OPERATION_TYPE_UPDATE

        self.property_skus = property_skus

    def get_json(self):
        data = PropertySkuSchema().dump(self.property_skus).data

        json_map = {'operation_type': self.operation,
                    'entity': messaging_constants.SKU_UNDER_PROPERTY_ENTITY, 'data': data}
        return json.loads(json.dumps(json_map, default=custom_json_encoder))


class SellerWrapper:
    def __init__(self, seller_details, created, deleted):
        if created:
            self.operation = messaging_constants.OPERATION_TYPE_CREATE
        elif deleted:
            self.operation = messaging_constants.OPERATION_TYPE_DELETE
        else:
            self.operation = messaging_constants.OPERATION_TYPE_UPDATE

        self.seller_details = seller_details

    def get_json(self):
        data = SellerResponseSchema().dump(self.seller_details).data

        json_map = {'operation_type': self.operation, 'entity': messaging_constants.SELLER_DETAILS, 'data': data}
        return json.loads(json.dumps(json_map, default=custom_json_encoder))


class SellerSkuWrapper:
    def __init__(self, seller_sku_details, created, deleted):
        if created:
            self.operation = messaging_constants.OPERATION_TYPE_CREATE
        elif deleted:
            self.operation = messaging_constants.OPERATION_TYPE_DELETE
        else:
            self.operation = messaging_constants.OPERATION_TYPE_UPDATE

        self.seller_sku_details = seller_sku_details

    def get_json(self):
        data = SellerSkuResponseSchema().dump(self.seller_sku_details).data

        json_map = {'operation_type': self.operation, 'entity': messaging_constants.SELLER_DETAILS, 'data': data}
        return json.loads(json.dumps(json_map, default=custom_json_encoder))


class RuptubLegalEntityDetailsWrapper:
    def __init__(self, ruptub_details_object, created, deleted):
        if created:
            self.operation = messaging_constants.OPERATION_TYPE_CREATE
        else:
            self.operation = messaging_constants.OPERATION_TYPE_UPDATE

        self.ruptub_details_object = ruptub_details_object

    def get_json(self):
        data = RuptubLegalEntityDetailsSchema().dump(self.ruptub_details_object).data

        json_map = {'operation_type': self.operation, 'entity': messaging_constants.RUPTUB_LEGAL_ENTITY_DETAILS,
                    'data': data}
        return json.loads(json.dumps(json_map, default=custom_json_encoder))


class MenuWrapper:
    def __init__(self, menu, created, deleted):
        if created:
            self.operation = messaging_constants.OPERATION_TYPE_CREATE
        elif deleted:
            self.operation = messaging_constants.OPERATION_TYPE_DELETE
        else:
            self.operation = messaging_constants.OPERATION_TYPE_UPDATE

        self.menu = menu

    def get_json(self):
        data = MenuMessageSchema().dump(self.menu).data
        event = dict(entity_name=messaging_constants.MENU_ENTITY, payload=data)
        json_map = {'operation_type': self.operation, 'entity': messaging_constants.MENU_ENTITY, 'events': [event], }

        return json.loads(json.dumps(json_map, default=custom_json_encoder))


class ComboWrapper:
    def __init__(self, combo, created, deleted):
        if created:
            self.operation = messaging_constants.OPERATION_TYPE_CREATE
        elif deleted:
            self.operation = messaging_constants.OPERATION_TYPE_DELETE
        else:
            self.operation = messaging_constants.OPERATION_TYPE_UPDATE

        self.combo = combo

    def get_json(self):
        data = ComboMessageSchema().dump(self.combo).data
        event = dict(entity_name=messaging_constants.COMBO_ENTITY, payload=data)
        json_map = {'operation_type': self.operation, 'entity': messaging_constants.COMBO_ENTITY, 'events': [event], }

        return json.loads(json.dumps(json_map, default=custom_json_encoder))


class ItemWrapper:
    def __init__(self, item, created, deleted):
        if created:
            self.operation = messaging_constants.OPERATION_TYPE_CREATE
        elif deleted:
            self.operation = messaging_constants.OPERATION_TYPE_DELETE
        else:
            self.operation = messaging_constants.OPERATION_TYPE_UPDATE

        self.item = item

    def get_json(self):
        data = ItemMessageSchema().dump(self.item).data
        event = dict(entity_name=messaging_constants.ITEM_ENTITY, payload=data)
        json_map = {'operation_type': self.operation, 'entity': messaging_constants.ITEM_ENTITY, 'events': [event], }

        return json.loads(json.dumps(json_map, default=custom_json_encoder))


class PropertyVideoWrapper:
    def __init__(self, item, created, deleted):
        if created:
            self.operation = messaging_constants.OPERATION_TYPE_CREATE
        elif deleted:
            self.operation = messaging_constants.OPERATION_TYPE_DELETE
        else:
            self.operation = messaging_constants.OPERATION_TYPE_UPDATE

        self.item = item

    def get_json(self):
        data = PropertyVideoSchema().dump(self.item).data
        return create_wrapper_json(self.operation,
                                   messaging_constants.PROPERTY_VIDEO_ENTITY,
                                   self.item.property.id,
                                   self.item.property.hx_id,
                                   data
                                   )
