"""
Property Department Repository
Repository implementation for property department operations following existing pattern.
"""

from typing import List, Optional

from object_registry import register_instance
from cataloging_service.infrastructure.repositories.base_entity_repository import (
    BaseEntityRepository,
)
from cataloging_service.infrastructure.adapters.property_department_adapter import (
    PropertyDepartmentAdapter,
)
from cataloging_service.domain.entities.properties.property_department import (
    PropertyDepartmentEntity,
)
from cataloging_service.models import Department


@register_instance()
class PropertyDepartmentRepository(BaseEntityRepository):
    """Repository for property department operations"""

    adaptor = PropertyDepartmentAdapter()

    def create(self, entity: PropertyDepartmentEntity) -> PropertyDepartmentEntity:
        """Create a new property department"""
        model = self.adaptor.to_db_model(entity)
        self.session().add(model)
        self.session().commit()
        return self.adaptor.to_entity(model)

    def get_by_id(self, department_id: int) -> Optional[PropertyDepartmentEntity]:
        """Get property department by ID"""
        model = self.rget_by_attr(Department, limit_one=True, id=department_id)
        return self.adaptor.to_entity(model) if model else None

    def get_by_property_and_code(
        self, property_id: str, code: str
    ) -> Optional[PropertyDepartmentEntity]:
        """Get property department by property ID and code"""
        model = self.rget_by_attr(
            Department, limit_one=True, property_id=property_id, code=code
        )
        return self.adaptor.to_entity(model) if model else None

    def get_by_property(
        self, property_id: str, active_only: bool = True
    ) -> List[PropertyDepartmentEntity]:
        """Get all departments for a property"""
        filters = {"property_id": property_id}
        if active_only:
            filters["is_active"] = True

        models = self.rget_by_attr(Department, **filters)
        return [self.adaptor.to_entity(model) for model in models]

    def update(self, entity: PropertyDepartmentEntity) -> PropertyDepartmentEntity:
        """Update property department"""
        existing_model = self.rget_by_attr(Department, limit_one=True, id=entity.id)
        if not existing_model:
            raise ValueError(f"Property department with ID {entity.id} not found")

        updated_model = self.adaptor.update_model_from_entity(existing_model, entity)
        self._update(updated_model)
        return self.adaptor.to_entity(updated_model)

    def delete(self, department_id: int) -> bool:
        """Delete property department"""
        model = self.rget_by_attr(Department, limit_one=True, id=department_id)
        if not model:
            return False

        self.delete(model)
        return True

    def exists_by_property_and_code(
        self, property_id: str, code: str, exclude_id: Optional[int] = None
    ) -> bool:
        """Check if department exists with given property and code"""
        query = (
            self.session()
            .query(Department)
            .filter_by(property_id=property_id, code=code)
        )
        if exclude_id:
            query = query.filter(Department.id != exclude_id)
        return query.first() is not None

    def get_by_codes(
        self, property_id: str, codes: List[str]
    ) -> List[PropertyDepartmentEntity]:
        """Get property departments by codes"""
        models = self.rget_by_attr(Department, property_id=property_id, code=codes)
        return [self.adaptor.to_entity(model) for model in models]

    def get_children(self, parent_id: int) -> List[PropertyDepartmentEntity]:
        """Get child departments for a parent"""
        models = self.rget_by_attr(Department, parent_id=parent_id, is_active=True)
        return [self.adaptor.to_entity(model) for model in models]

    def get_root_departments(self, property_id: str) -> List[PropertyDepartmentEntity]:
        """Get root departments (no parent) for a property"""
        query = (
            self.session()
            .query(Department)
            .filter_by(property_id=property_id, is_active=True, parent_id=None)
        )
        models = query.all()
        return [self.adaptor.to_entity(model) for model in models]

    def get_template_derived(self, property_id: str) -> List[PropertyDepartmentEntity]:
        """Get departments created from templates"""
        query = (
            self.session()
            .query(Department)
            .filter(
                Department.property_id == property_id,
                Department.created_from_template_code.isnot(None),
                Department.is_active == True,
            )
        )
        models = query.all()
        return [self.adaptor.to_entity(model) for model in models]

    def get_custom_departments(
        self, property_id: str
    ) -> List[PropertyDepartmentEntity]:
        """Get custom departments (not from templates)"""
        models = self.rget_by_attr(
            Department, property_id=property_id, is_custom=True, is_active=True
        )
        return [self.adaptor.to_entity(model) for model in models]

    def count_by_property(self, property_id: str, active_only: bool = True) -> int:
        """Count departments for a property"""
        filters = {"property_id": property_id}
        if active_only:
            filters["is_active"] = True

        models = self.rget_by_attr(Department, **filters)
        return len(models)
