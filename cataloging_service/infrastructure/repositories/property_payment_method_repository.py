"""
Property Payment Method Repository
Repository implementation for property payment method operations following existing pattern.
"""

from typing import List, Optional

from object_registry import register_instance
from cataloging_service.infrastructure.repositories.base_entity_repository import (
    BaseEntityRepository,
)
from cataloging_service.infrastructure.adapters.property_payment_method_adapter import (
    PropertyPaymentMethodAdapter,
)
from cataloging_service.domain.entities.properties.property_payment_method import (
    PropertyPaymentMethodEntity,
)
from cataloging_service.models import PropertyPaymentMethod


@register_instance()
class PropertyPaymentMethodRepository(BaseEntityRepository):
    """Repository for property payment method operations"""

    adaptor = PropertyPaymentMethodAdapter()

    def create(
        self, entity: PropertyPaymentMethodEntity
    ) -> PropertyPaymentMethodEntity:
        """Create a new property payment method"""
        model = self.adaptor.to_db_model(entity)
        self.persist(model)
        return self.adaptor.to_entity(model)

    def get_by_id(
        self, property_payment_method_id: int
    ) -> Optional[PropertyPaymentMethodEntity]:
        """Get property payment method by ID"""
        model = self.rget_by_attr(
            PropertyPaymentMethod, limit_one=True, id=property_payment_method_id
        )
        return self.adaptor.to_entity(model) if model else None

    def get_by_property_and_payment_method(
        self, property_id: str, payment_method_id: int
    ) -> Optional[PropertyPaymentMethodEntity]:
        """Get property payment method by property ID and payment method ID"""
        model = self.rget_by_attr(
            PropertyPaymentMethod,
            limit_one=True,
            property_id=property_id,
            payment_method_id=payment_method_id,
        )
        return self.adaptor.to_entity(model) if model else None

    def get_by_property(
        self, property_id: str, active_only: bool = True
    ) -> List[PropertyPaymentMethodEntity]:
        """Get all payment methods for a property"""
        filters = {"property_id": property_id}
        if active_only:
            filters["is_active"] = True

        models = self.rget_by_attr(PropertyPaymentMethod, **filters)
        return [self.adaptor.to_entity(model) for model in models]

    def update(
        self, entity: PropertyPaymentMethodEntity
    ) -> PropertyPaymentMethodEntity:
        """Update property payment method"""
        existing_model = self.rget_by_attr(
            PropertyPaymentMethod, limit_one=True, id=entity.id
        )
        if not existing_model:
            raise ValueError(f"Property payment method with ID {entity.id} not found")

        updated_model = self.adaptor.update_model_from_entity(existing_model, entity)
        self._update(updated_model)
        return self.adaptor.to_entity(updated_model)

    def delete(self, property_payment_method_id: int) -> bool:
        """Delete property payment method"""
        model = self.rget_by_attr(
            PropertyPaymentMethod, limit_one=True, id=property_payment_method_id
        )
        if not model:
            return False

        self.delete(model)
        return True

    def exists_by_property_and_payment_method(
        self, property_id: str, payment_method_id: int, exclude_id: Optional[int] = None
    ) -> bool:
        """Check if property payment method exists"""
        query = (
            self.session()
            .query(PropertyPaymentMethod)
            .filter_by(property_id=property_id, payment_method_id=payment_method_id)
        )
        if exclude_id:
            query = query.filter(PropertyPaymentMethod.id != exclude_id)
        return query.first() is not None

    def get_by_payment_method_ids(
        self, property_id: str, payment_method_ids: List[int]
    ) -> List[PropertyPaymentMethodEntity]:
        """Get property payment methods by payment method IDs"""
        models = self.rget_by_attr(
            PropertyPaymentMethod,
            property_id=property_id,
            payment_method_id=payment_method_ids,
        )
        return [self.adaptor.to_entity(model) for model in models]

    def get_configured_methods(
        self, property_id: str
    ) -> List[PropertyPaymentMethodEntity]:
        """Get configured payment methods (with config) for a property"""
        query = (
            self.session()
            .query(PropertyPaymentMethod)
            .filter(
                PropertyPaymentMethod.property_id == property_id,
                PropertyPaymentMethod.config.isnot(None),
                PropertyPaymentMethod.is_active == True,
            )
        )
        models = query.all()
        return [self.adaptor.to_entity(model) for model in models]

    def get_gateway_supported_methods(
        self, property_id: str
    ) -> List[PropertyPaymentMethodEntity]:
        """Get payment methods with gateway support for a property"""
        query = (
            self.session()
            .query(PropertyPaymentMethod)
            .filter(
                PropertyPaymentMethod.property_id == property_id,
                PropertyPaymentMethod.config.op("->>")("gateway").isnot(None),
                PropertyPaymentMethod.is_active == True,
            )
        )
        models = query.all()
        return [self.adaptor.to_entity(model) for model in models]

    def count_by_property(self, property_id: str, active_only: bool = True) -> int:
        """Count payment methods for a property"""
        filters = {"property_id": property_id}
        if active_only:
            filters["is_active"] = True

        models = self.rget_by_attr(PropertyPaymentMethod, **filters)
        return len(models)

    def get_all_properties_with_payment_method(
        self, payment_method_id: int
    ) -> List[str]:
        """Get all property IDs that use a specific payment method"""
        models = self.rget_by_attr(
            PropertyPaymentMethod, payment_method_id=payment_method_id, is_active=True
        )
        return [model.property_id for model in models]
