"""
Property Profit Center Repository
Repository implementation for property profit center operations following existing pattern.
"""

from typing import List, Optional

from object_registry import register_instance
from cataloging_service.infrastructure.repositories.base_entity_repository import (
    BaseEntityRepository,
)
from cataloging_service.infrastructure.adapters.property_profit_center_adapter import (
    PropertyProfitCenterAdapter,
)
from cataloging_service.domain.entities.properties.property_profit_center import (
    PropertyProfitCenterEntity,
)
from cataloging_service.models import ProfitCenter


@register_instance()
class PropertyProfitCenterRepository(BaseEntityRepository):
    """Repository for property profit center operations"""

    adaptor = PropertyProfitCenterAdapter()

    def create(self, entity: PropertyProfitCenterEntity) -> PropertyProfitCenterEntity:
        """Create a new property profit center"""
        model = self.adaptor.to_db_model(entity)
        # self.persist(model)
        self.session().add(model)
        self.session().commit()
        return self.adaptor.to_entity(model)

    def get_by_id(self, profit_center_id: str) -> Optional[PropertyProfitCenterEntity]:
        """Get property profit center by ID"""
        model = self.rget_by_attr(ProfitCenter, limit_one=True, id=profit_center_id)
        return self.adaptor.to_entity(model) if model else None

    def get_by_property_and_code(
        self, property_id: str, code: str
    ) -> Optional[PropertyProfitCenterEntity]:
        """Get property profit center by property ID and code"""
        model = self.rget_by_attr(
            ProfitCenter, limit_one=True, property_id=property_id, code=code
        )
        return self.adaptor.to_entity(model) if model else None

    def get_by_property(
        self, property_id: str, active_only: bool = True
    ) -> List[PropertyProfitCenterEntity]:
        """Get all profit centers for a property"""
        filters = {"property_id": property_id}
        if active_only:
            filters["is_active"] = True

        models = self.rget_by_attr(ProfitCenter, **filters)
        return [self.adaptor.to_entity(model) for model in models]

    def get_by_department(self, department_id: int) -> List[PropertyProfitCenterEntity]:
        """Get profit centers for a specific department"""
        models = self.rget_by_attr(
            ProfitCenter, department_id=department_id, is_active=True
        )
        return [self.adaptor.to_entity(model) for model in models]

    def update(self, entity: PropertyProfitCenterEntity) -> PropertyProfitCenterEntity:
        """Update property profit center"""
        existing_model = self.rget_by_attr(ProfitCenter, limit_one=True, id=entity.id)
        if not existing_model:
            raise ValueError(f"Property profit center with ID {entity.id} not found")

        updated_model = self.adaptor.update_model_from_entity(existing_model, entity)
        self._update(updated_model)
        return self.adaptor.to_entity(updated_model)

    def delete(self, profit_center_id: str) -> bool:
        """Delete property profit center"""
        model = self.rget_by_attr(ProfitCenter, limit_one=True, id=profit_center_id)
        if not model:
            return False

        self.delete(model)
        return True

    def exists_by_property_and_code(
        self, property_id: str, code: str, exclude_id: Optional[str] = None
    ) -> bool:
        """Check if profit center exists with given property and code"""
        query = (
            self.session()
            .query(ProfitCenter)
            .filter_by(property_id=property_id, code=code)
        )
        if exclude_id:
            query = query.filter(ProfitCenter.id != exclude_id)
        return query.first() is not None

    def get_by_codes(
        self, property_id: str, codes: List[str]
    ) -> List[PropertyProfitCenterEntity]:
        """Get property profit centers by codes"""
        models = self.rget_by_attr(ProfitCenter, property_id=property_id, code=codes)
        return [self.adaptor.to_entity(model) for model in models]

    def get_auto_created(self, property_id: str) -> List[PropertyProfitCenterEntity]:
        """Get auto-created profit centers for a property"""
        models = self.rget_by_attr(
            ProfitCenter, property_id=property_id, is_auto_created=True, is_active=True
        )
        return [self.adaptor.to_entity(model) for model in models]

    def get_template_derived(
        self, property_id: str
    ) -> List[PropertyProfitCenterEntity]:
        """Get profit centers created from templates"""
        query = (
            self.session()
            .query(ProfitCenter)
            .filter(
                ProfitCenter.property_id == property_id,
                ProfitCenter.created_from_template_code.isnot(None),
                ProfitCenter.is_active == True,
            )
        )
        models = query.all()
        return [self.adaptor.to_entity(model) for model in models]

    def get_custom_profit_centers(
        self, property_id: str
    ) -> List[PropertyProfitCenterEntity]:
        """Get custom profit centers (not from templates)"""
        models = self.rget_by_attr(
            ProfitCenter, property_id=property_id, is_custom=True, is_active=True
        )
        return [self.adaptor.to_entity(model) for model in models]

    def get_with_system_interface(
        self, property_id: str
    ) -> List[PropertyProfitCenterEntity]:
        """Get profit centers with system interface"""
        query = (
            self.session()
            .query(ProfitCenter)
            .filter(
                ProfitCenter.property_id == property_id,
                ProfitCenter.system_interface.isnot(None),
                ProfitCenter.is_active == True,
            )
        )
        models = query.all()
        return [self.adaptor.to_entity(model) for model in models]

    def count_by_department(self, department_id: int, active_only: bool = True) -> int:
        """Count profit centers for a department"""
        filters = {"department_id": department_id}
        if active_only:
            filters["is_active"] = True

        models = self.rget_by_attr(ProfitCenter, **filters)
        return len(models)

    def count_by_property(self, property_id: str, active_only: bool = True) -> int:
        """Count profit centers for a property"""
        filters = {"property_id": property_id}
        if active_only:
            filters["is_active"] = True

        models = self.rget_by_attr(ProfitCenter, **filters)
        return len(models)
