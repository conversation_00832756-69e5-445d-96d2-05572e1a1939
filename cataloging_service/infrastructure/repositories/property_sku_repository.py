"""
Property SKU Repository
Repository implementation for property SKU operations following existing pattern.
"""

from typing import List, Optional

from object_registry import register_instance
from cataloging_service.infrastructure.repositories.base_entity_repository import (
    BaseEntityRepository,
)
from cataloging_service.infrastructure.adapters.property_sku_adapter import (
    PropertySkuAdapter,
)
from cataloging_service.domain.entities.properties.property_sku import PropertySkuEntity
from cataloging_service.models import Sku


@register_instance()
class PropertySkuRepository(BaseEntityRepository):
    """Repository for property SKU operations"""

    adaptor = PropertySkuAdapter()

    def create(self, entity: PropertySkuEntity) -> PropertySkuEntity:
        """Create a new property SKU"""
        model = self.adaptor.to_db_model(entity)
        self.session().add(model)
        self.session().commit()
        return self.adaptor.to_entity(model)

    def get_by_id(self, sku_id: int) -> Optional[PropertySkuEntity]:
        """Get property SKU by ID"""
        model = self.rget_by_attr(Sku, limit_one=True, id=sku_id)
        return self.adaptor.to_entity(model) if model else None

    def get_by_property_and_code(
        self, property_id: str, code: str
    ) -> Optional[PropertySkuEntity]:
        """Get property SKU by property ID and code"""
        model = self.rget_by_attr(
            Sku, limit_one=True, property_id=property_id, code=code
        )
        return self.adaptor.to_entity(model) if model else None

    def get_by_property(
        self, property_id: str, active_only: bool = True
    ) -> List[PropertySkuEntity]:
        """Get all SKUs for a property"""
        filters = {"property_id": property_id}
        if active_only:
            filters["is_active"] = True

        models = self.rget_by_attr(Sku, **filters)
        return [self.adaptor.to_entity(model) for model in models]

    def get_by_profit_center(self, profit_center_id: str) -> List[PropertySkuEntity]:
        """Get SKUs for a specific profit center"""
        models = self.rget_by_attr(
            Sku, profit_center_id=profit_center_id, is_active=True
        )
        return [self.adaptor.to_entity(model) for model in models]

    def update(self, entity: PropertySkuEntity) -> PropertySkuEntity:
        """Update property SKU"""
        existing_model = self.rget_by_attr(Sku, limit_one=True, id=entity.id)
        if not existing_model:
            raise ValueError(f"Property SKU with ID {entity.id} not found")

        updated_model = self.adaptor.update_model_from_entity(existing_model, entity)
        self._update(updated_model)
        return self.adaptor.to_entity(updated_model)

    def delete(self, sku_id: int) -> bool:
        """Delete property SKU"""
        model = self.rget_by_attr(Sku, limit_one=True, id=sku_id)
        if not model:
            return False

        self.delete(model)
        return True

    def exists_by_property_and_code(
        self, property_id: str, code: str, exclude_id: Optional[int] = None
    ) -> bool:
        """Check if SKU exists with given property and code"""
        query = self.session().query(Sku).filter_by(property_id=property_id, code=code)
        if exclude_id:
            query = query.filter(Sku.id != exclude_id)
        return query.first() is not None

    def get_by_codes(
        self, property_id: str, codes: List[str]
    ) -> List[PropertySkuEntity]:
        """Get property SKUs by codes"""
        models = self.rget_by_attr(Sku, property_id=property_id, code=codes)
        return [self.adaptor.to_entity(model) for model in models]

    def get_by_department(self, department_id: int) -> List[PropertySkuEntity]:
        """Get SKUs for a specific department"""
        models = self.rget_by_attr(Sku, department_id=department_id, is_active=True)
        return [self.adaptor.to_entity(model) for model in models]

    def get_template_derived(self, property_id: str) -> List[PropertySkuEntity]:
        """Get SKUs created from templates"""
        query = (
            self.session()
            .query(Sku)
            .filter(
                Sku.property_id == property_id,
                Sku.created_from_template_code.isnot(None),
                Sku.is_active == True,
            )
        )
        models = query.all()
        return [self.adaptor.to_entity(model) for model in models]

    def get_custom_skus(self, property_id: str) -> List[PropertySkuEntity]:
        """Get custom SKUs (not from templates)"""
        models = self.rget_by_attr(
            Sku, property_id=property_id, is_custom=True, is_active=True
        )
        return [self.adaptor.to_entity(model) for model in models]

    def get_saleable_skus(self, property_id: str) -> List[PropertySkuEntity]:
        """Get saleable SKUs (with price) for a property"""
        query = (
            self.session()
            .query(Sku)
            .filter(
                Sku.property_id == property_id,
                Sku.price.isnot(None),
                Sku.price > 0,
                Sku.is_active == True,
            )
        )
        models = query.all()
        return [self.adaptor.to_entity(model) for model in models]

    def get_by_category(
        self, property_id: str, category_id: int
    ) -> List[PropertySkuEntity]:
        """Get SKUs by category for a property"""
        models = self.rget_by_attr(
            Sku, property_id=property_id, sku_category_id=category_id, is_active=True
        )
        return [self.adaptor.to_entity(model) for model in models]

    def get_by_price_range(
        self, property_id: str, min_price: float, max_price: float
    ) -> List[PropertySkuEntity]:
        """Get SKUs within a price range"""
        query = (
            self.session()
            .query(Sku)
            .filter(
                Sku.property_id == property_id,
                Sku.price >= min_price,
                Sku.price <= max_price,
                Sku.is_active == True,
            )
        )
        models = query.all()
        return [self.adaptor.to_entity(model) for model in models]

    def count_by_profit_center(
        self, profit_center_id: str, active_only: bool = True
    ) -> int:
        """Count SKUs for a profit center"""
        filters = {"profit_center_id": profit_center_id}
        if active_only:
            filters["is_active"] = True

        models = self.rget_by_attr(Sku, **filters)
        return len(models)

    def count_by_property(self, property_id: str, active_only: bool = True) -> int:
        """Count SKUs for a property"""
        filters = {"property_id": property_id}
        if active_only:
            filters["is_active"] = True

        models = self.rget_by_attr(Sku, **filters)
        return len(models)
