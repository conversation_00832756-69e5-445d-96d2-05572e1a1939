from sqlalchemy import func

from cataloging_service.infrastructure.repositories.base_repository import BaseRepository
from cataloging_service.models import RoomRackRateModel, RoomType


class RoomRackRateRepository(BaseRepository):

    def get_room_rack_rates_by_ids(self, room_rack_rate_ids):
        return RoomRackRateModel.query.filter(
            RoomRackRateModel.room_rack_rate_id.in_(room_rack_rate_ids)).all()

    def get_room_rack_rate_by_id(self, room_rack_rate_id):
        return RoomRackRateModel.query.filter(
            RoomRackRateModel.room_rack_rate_id == room_rack_rate_id).first()

    def get_room_rack_rates(self, property_id):
        return RoomRackRateModel.query.filter(RoomRackRateModel.property_id == property_id).all()

    def save_room_rack_rate(self, room_rack_rate):
        self.persist(room_rack_rate)

    def get_room_rack_rates_count(self, property_id):
        return RoomRackRateModel \
            .query.with_entities(RoomRackRateModel.room_type_id,
                                 func.count(RoomRackRateModel.room_type_id)). \
            group_by(RoomRackRateModel.room_type_id) \
            .filter(RoomRackRateModel.property_id == property_id).all()
