from cataloging_service.infrastructure.repositories.base_repository import BaseRepository
from cataloging_service.models import SellerSku, MenuCategory


class SellerSkuRepository(BaseRepository):

    @staticmethod
    def get_seller_sku(seller_id, sku_id):
        return SellerSku.query.filter(SellerSku.seller_id == seller_id, SellerSku.sku_id == sku_id).one()

    @staticmethod
    def get_seller_skus(seller_id):
        return SellerSku.query.filter(SellerSku.seller_id == seller_id).all()

    @staticmethod
    def get_all_seller_skus():
        return SellerSku.query.all()


class MenuCategoryRepository(BaseRepository):
    @staticmethod
    def get_menu_categories(seller_category_id=None):
        query = MenuCategory.query
        if seller_category_id:
            query = query.filter(MenuCategory.seller_category_id == seller_category_id)
        return query.all()
