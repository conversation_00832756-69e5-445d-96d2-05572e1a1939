"""
Transaction Default Mapping Repository
"""

from object_registry import register_instance
from cataloging_service.infrastructure.repositories.base_entity_repository import (
    BaseEntityRepository,
)
from cataloging_service.infrastructure.adapters.transaction_default_mapping_adapter import TransactionDefaultMappingAdapter
from cataloging_service.domain.entities.transactions.transaction_default_mapping import TransactionDefaultMappingEntity
from cataloging_service.models import TransactionDefaultMapping


@register_instance()
class TransactionDefaultMappingRepository(BaseEntityRepository):
    adaptor = TransactionDefaultMappingAdapter()

    def create(self, entity: TransactionDefaultMappingEntity) -> TransactionDefaultMappingEntity:
        model = self.adaptor.to_db_model(entity)
        self.session().add(model)
        self.session().commit()
        return self.adaptor.to_entity(model)

    def get_transaction_default_mapping(self, transaction_type: str, transaction_type_code: str, entity_type: str, brand_id: int) -> TransactionDefaultMappingEntity:
        model = self.rget_by_attr(TransactionDefaultMapping, limit_one=True, transaction_type=transaction_type, transaction_type_code=transaction_type_code, entity_type=entity_type, brand_id=brand_id)
        return self.adaptor.to_entity(model) if model else None


