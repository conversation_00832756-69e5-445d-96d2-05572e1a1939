import json

import datetime as dt

from cataloging_service.integration_tests.utilities import excel_utils
from cataloging_service.integration_tests.utilities.common_utils import sanitize_test_data, del_none


class CreateItemBuilder(object):
    def __init__(self, sheet_name, test_case_id):
        self.test_case_id = test_case_id
        self.sheet_name = sheet_name

    def create_item_request(self):
        item_data_from_sheet = self.get_test_data(self.sheet_name, self.test_case_id)
        customised_items_from_item_sheet = self.get_customised_items(item_data_from_sheet[0]['customised_item'])
        item_variants_from_item_sheet = self.get_item_variants(item_data_from_sheet[0]['item_variants'])
        side_items_from_item_sheet = self.get_side_items(item_data_from_sheet[0]['side_items'])
        current_datetime = dt.datetime.now()
        code = item_data_from_sheet[0]['name'] + " " + str(current_datetime.microsecond)
        response_json = {
            'code': code if self.test_case_id not in ['Item_63'] else None,
            'active': sanitize_test_data(item_data_from_sheet[0]['active']),
            'allergen_info': sanitize_test_data(item_data_from_sheet[0]['allergen_info']),
            'calorie_info': sanitize_test_data(item_data_from_sheet[0]['calorie_info']),
            'image': sanitize_test_data(item_data_from_sheet[0]['image']),
            'contains_alcohol': sanitize_test_data(item_data_from_sheet[0]['contains_alcohol']),
            'cost': sanitize_test_data(item_data_from_sheet[0]['cost']),
            'item_customisations': customised_items_from_item_sheet,
            'description': sanitize_test_data(item_data_from_sheet[0]['description']),
            'food_type': sanitize_test_data(item_data_from_sheet[0]['food_type']),
            'item_variants': item_variants_from_item_sheet,
            'name': sanitize_test_data(item_data_from_sheet[0]['name']),
            'display_name': sanitize_test_data(item_data_from_sheet[0]['display_name']),
            'pre_tax_price': sanitize_test_data(item_data_from_sheet[0]['pre_tax_price']),
            'prep_time': sanitize_test_data(item_data_from_sheet[0]['prep_time']),
            'print_name': sanitize_test_data(item_data_from_sheet[0]['print_name']),
            'side_items': side_items_from_item_sheet,
            'sku_category_code': sanitize_test_data(item_data_from_sheet[0]['sku_category_code']),
            'sold_out': sanitize_test_data(item_data_from_sheet[0]['sold_out']),
            'use_as_side': sanitize_test_data(item_data_from_sheet[0]['use_as_side']),
            'kitchen_id': sanitize_test_data(item_data_from_sheet[0]['kitchen_id'])

        }

        return json.dumps(del_none(response_json))

    @staticmethod
    def get_test_data(sheet_name, test_case_id):
        return excel_utils.get_test_case_data(sheet_name, test_case_id)

    @staticmethod
    def get_customised_items(customised_items):
        if sanitize_test_data(customised_items) and customised_items != 'NULL':
            return json.loads(customised_items)
        elif customised_items == 'Empty Array' or customised_items == 'NULL':
            return sanitize_test_data(customised_items)
        else:
            return None

    def get_item_variants(self, item_variants):
        if sanitize_test_data(item_variants) and item_variants != 'NULL':
            variant_list = json.loads(item_variants)
            item_variant_list = []
            for item_variant in variant_list:
                variant_json = {
                    "name": sanitize_test_data(item_variant['name']),
                    "pre_tax_price": float(sanitize_test_data(item_variant['pre_tax_price']))
                    if item_variant['pre_tax_price'] not in ['', 'NULL'] else sanitize_test_data(
                        item_variant['pre_tax_price']),
                    "sku_category_code": sanitize_test_data(item_variant['sku_category_code']),
                    "cost": float(sanitize_test_data(item_variant['cost']))
                    if item_variant['cost'] not in ['', 'NULL'] else sanitize_test_data(item_variant['cost'])
                }
                variant_list = []
                item_customization_list = []
                for variant in item_variant['variants']:
                    variant_list.append(
                        {"variant_id": variant['variant_id'], "variant_group_id": variant['variant_group_id']})
                if item_variant.get('item_customisations'):
                    for item_customization in item_variant['item_customisations']:
                        item_customization_list.append(
                            {"cost": item_customization['cost'], "delta_price": item_customization['delta_price'],
                             "variant_id": item_customization['variant_id'],
                             "variant_group_id": item_customization['variant_group_id']})

                variant_json['variants'] = variant_list
                variant_json['item_customisations'] = item_customization_list
                json.dumps(del_none(variant_json))
                item_variant_list.append(variant_json)
            return item_variant_list
        elif item_variants == 'Empty Array' or item_variants == 'NULL':
            return sanitize_test_data(item_variants)
        else:
            return None

    @staticmethod
    def get_side_items(side_items):
        if sanitize_test_data(side_items) and side_items != 'NULL':
            return json.loads(side_items)
        elif side_items == 'Empty Array' or side_items == 'NULL':
            return sanitize_test_data(side_items)
        else:
            return None
