import json, ast
import datetime as dt
from cataloging_service.integration_tests.config import sheet_names
from cataloging_service.integration_tests.utilities import excel_utils
from cataloging_service.integration_tests.utilities.common_utils import sanitize_test_data, del_none


class CreateMenuBuilder(object):
    def __init__(self, sheet_name, test_case_id):
        self.test_case_id = test_case_id
        self.sheet_name = sheet_name

    def create_menu_request(self):
        menu_data_from_sheet = self.get_test_data(self.sheet_name, self.test_case_id)
        menu_timing = self.get_menu_timings(menu_data_from_sheet[0]['menu_timings'])
        menu_type = self.get_menu_types(menu_data_from_sheet[0]['menu_types'])
        current_datetime = dt.datetime.now()
        code = menu_data_from_sheet[0]['name'] + " " + str(current_datetime.microsecond)
        menu_request = {
            'code': code if self.test_case_id not in ['Menu_27'] else None,
            'description': sanitize_test_data(menu_data_from_sheet[0]['description']),
            'display_name': sanitize_test_data(menu_data_from_sheet[0]['display_name']),
            'menu_timings': menu_timing,
            'name': sanitize_test_data(menu_data_from_sheet[0]['name']),
            'menu_types': menu_type
        }
        return json.dumps(del_none(menu_request))

    @staticmethod
    def get_test_data(sheet_name, test_case_id):
        return excel_utils.get_test_case_data(sheet_name, test_case_id)

    @staticmethod
    def get_menu_timings(menu_timing):
        return ast.literal_eval(menu_timing) if menu_timing not in ['Empty Array', '', 'NULL'] else sanitize_test_data(
            menu_timing)

    def update_menu_request(self):
        update_menu_test_data = self.get_test_data(self.sheet_name, self.test_case_id)[0]
        menu_items = self.get_menu_items(update_menu_test_data['menu_item'])
        menu_timing = self.get_menu_timings(update_menu_test_data['menu_timings'])
        menu_combos = self.get_menu_timings(update_menu_test_data['menu_combos'])
        menu_types = self.get_menu_timings(update_menu_test_data['menu_types'])
        menu_categories = self.get_menu_categories(update_menu_test_data['menu_categories'])
        current_datetime = dt.datetime.now()
        code = update_menu_test_data['name'] + " " + str(current_datetime.microsecond)
        update_menu_request = {
            'code': code,
            'description': sanitize_test_data(update_menu_test_data['description']),
            'display_name': sanitize_test_data(update_menu_test_data['display_name']),
            'menu_items': menu_items,
            'menu_categories': menu_categories,
            'menu_combos': menu_combos,
            'menu_timings': menu_timing,
            "menu_types": menu_types,
            'name': sanitize_test_data(update_menu_test_data['name']),
        }
        return json.dumps(del_none(update_menu_request))

    @staticmethod
    def get_menu_items(menu_items):
        return ast.literal_eval(menu_items) if menu_items not in ['Empty Array', '', 'NULL'] else sanitize_test_data(
            menu_items)

    @staticmethod
    def get_menu_categories(menu_categories):
        return ast.literal_eval(menu_categories) if menu_categories not in ['Empty Array', '',
                                                                            'NULL'] else sanitize_test_data(
            menu_categories)

    @staticmethod
    def get_menu_types(menu_types):
        return ast.literal_eval(menu_types) if menu_types not in ['Empty Array', '', 'NULL',
                                                                  'EMPTY'] else sanitize_test_data(
            menu_types)
