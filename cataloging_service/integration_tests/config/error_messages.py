import enum


class ErrorMessage(enum.Enum):
    Item_05 = [{'contains_alcohol': ['Missing data for required field.']}]
    Item_08 = [{'contains_alcohol': ['Field may not be null.']}]
    Item_10 = [{'cost': ['Field may not be null.']}]
    Item_20 = [{'name': ['Missing data for required field.']}]
    Item_21 = [{'name': ['Field may not be null.']}]
    Item_28 = [{'prep_time': ['Missing data for required field.']}]
    Item_29 = [{'prep_time': ['Field may not be null.']}]
    Item_30 = [{"prep_time": ["prep_time field has wrong format"]}]
    Item_31 = [{"prep_time": ["prep_time field has wrong format"]}]
    Item_38 = [{"item_variants": ["Field may not be null."]}]
    Item_41 = [{"side_items": ["Field may not be null."]}]
    Item_44 = [{"side_items": ["Side Items ids are either duplicate, doesn't exist or can't be used as side items"]}]
    Item_51 = [{"food_type": ["Field may not be null."]}]
    Item_52 = [{"food_type": ["Invalid value."]}]
    Item_53 = [{"food_type": ["Invalid value."]}]
    Item_63 = [{"code": ["Missing data for required field."]}]

    Patch_Item_13_1 = [
        {"side_items": ["Side Items ids are either duplicate, doesn't exist or can't be used as side items"]}]
    Patch_Item_41 = [{"food_type": ["Field may not be null."]}]
    Patch_Item_42 = [{"food_type": ["Invalid value."]}]

    Menu_11 = [{'menu_timings': ['Field may not be null.']}]
    Menu_13 = [{'menu_timings': {'0': {'start_time': ['Missing data for required field.']}}}]
    Menu_14 = [{'menu_timings': {'0': {'end_time': ['Missing data for required field.']}}}]
    Menu_15 = [{'menu_timings': {'0': {'days': ['Missing data for required field.']}}}]
    Menu_18 = [{"name": ["Missing data for required field."]}]
    Menu_20 = [{"name": ["Field may not be null."]}]
    Menu_22 = [[
        "duplicate key value violates unique constraint \"menu_name_seller_id_unique_key\"\nDETAIL:  "
        "Key (seller_id, name)=(1,  Name 21 @12345) already exists.\n"]]
    Menu_23 = [{"menu_types": ["Missing data for required field."]}]
    Menu_24 = [{"menu_types": ["Not a valid list."]}]
    Menu_25 = [{"menu_types": ["Field may not be null."]}]
    Menu_26 = [{"menu_types": ["Missing data for required field."]}]
    Menu_27 = [{"code": ["Missing data for required field."]}]

    Patch_Menu_22 = [{"menu_timings": ["Field may not be null."]}]

    variant_03 = [{"can_select_multiple": ["Not a valid boolean."]}]
    variant_04 = [{"can_select_multiple": ["Missing data for required field."]}]
    variant_05 = [{"can_select_multiple": ["Field may not be null."]}]
    variant_06 = [{"can_select_multiple": ["Not a valid boolean."]}]
    variant_08 = [{"can_select_multiple": ["Field may not be null."], "can_select_quantity": ["Not a valid boolean."]}]
    variant_09 = [{"can_select_quantity": ["Missing data for required field."]}]
    variant_10 = [{"can_select_quantity": ["Field may not be null."]}]
    variant_11 = [{"can_select_quantity": ["Not a valid boolean."]}]
    variant_12 = [
        {"can_select_multiple": ["Missing data for required field."],
         "can_select_quantity": ["Missing data for required field."]}]
    variant_13 = [
        {"can_select_multiple": ["Field may not be null."], "can_select_quantity": ["Field may not be null."]}]
    variant_14 = [{"can_select_multiple": ["Not a valid boolean."], "can_select_quantity": ["Not a valid boolean."]}]
    variant_23 = [{"is_customisation": ["Not a valid boolean."]}]
    variant_24 = [{"is_customisation": ["Field may not be null."]}]
    variant_25 = [{"is_customisation": ["Not a valid boolean."]}]
    variant_26 = [{'is_customisation': ['Missing data for required field.']}]
    variant_28 = [{"maximum_selectable_quantity": ["Not a valid integer."]}]
    variant_31 = [{"maximum_selectable_quantity": ["Not a valid integer."]}]
    variant_33 = [{"minimum_selectable_quantity": ["Not a valid integer."]}]
    variant_36 = [{"name": ["Field may not be null."]}]
    variant_37 = [{"name": ["Missing data for required field."]}]
    variant_39 = [{"name": ["Not a valid string."]}]

    combo_02 = [{"combo_items": ["Field may not be null."]}]
    combo_05 = [{"combo_items": {"0": {"item_id": ["Not a valid integer."]}}}]
    combo_06 = [{"contains_alcohol": ["Field may not be null."]}]
    combo_07 = [{"contains_alcohol": ["Not a valid boolean."]}]
    combo_08 = [{"contains_alcohol": ["Not a valid boolean."]}]
    combo_11 = [{"cost": ["Field may not be null."]}]
    combo_12 = [{"cost": ["Not a valid number."]}]
    combo_13 = [{"cost": ["Missing data for required field."]}]
    combo_14 = [{"cost": ["Not a valid number."]}]
    combo_25 = [{"name": ["Field may not be null."]}]
    combo_28 = [{"name": ["Missing data for required field."]}]

    combo_31 = [{"pre_tax_price": ["Field may not be null."]}]
    combo_32 = [{"pre_tax_price": ["Not a valid number."]}]
    combo_33 = [{"pre_tax_price": ["Not a valid number."]}]
    combo_34 = [{"pre_tax_price": ["Missing data for required field."]}]
    combo_35 = [{"prep_time": ["Not a valid string."]}]
    combo_36 = [{"prep_time": ["Field may not be null."]}]

    combo_37 = [{"prep_time": ["prep_time field has wrong format"]}]
    combo_38 = [{"prep_time": ["prep_time field has wrong format"]}]
    combo_39 = [{"prep_time": ["Missing data for required field."]}]
    combo_43 = [{"is_available": ["Not a valid boolean."]}]
    combo_50 = [{"code": ["Missing data for required field."]}]

    variant_49 = [{"variants": ["Field may not be null."]}]

    area_03 = [{"name": ["Field may not be null."]}]
    area_09 = [{"tables": {"0": {"number_of_tables": ["Not a valid integer."]}}}]
    area_10 = [{"tables": {"0": {"number_of_tables": ["Not a valid integer."]}}}]
    area_11 = [{"tables": {"0": {"seats_count": ["Not a valid integer."]}}}]
    area_12 = [{"tables": {"0": {"seats_count": ["Not a valid integer."]}}}]

    patch_area_03 = [{"name": ["Field may not be null."]}]
