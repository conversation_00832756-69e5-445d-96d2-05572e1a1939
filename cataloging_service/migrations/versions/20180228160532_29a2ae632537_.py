"""empty message

Revision ID: 29a2ae632537
Revises: d19789ed76a3
Create Date: 2018-02-28 16:05:32.533101

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '29a2ae632537'
down_revision = 'd19789ed76a3'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('room', sa.Column('room_size', sa.DECIMAL(), nullable=True))
    op.add_column('room_type_configuration', sa.Column('min_room_size', sa.DECIMAL(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('room_type_configuration', 'min_room_size')
    op.drop_column('room', 'room_size')
    # ### end Alembic commands ###
