"""empty message

Revision ID: 1ac3b829d865
Revises: d2a000527bf7
Create Date: 2018-10-31 16:51:38.309035

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '1ac3b829d865'
down_revision = 'd2a000527bf7'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('brand',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('code', sa.String(length=100), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('display_name', sa.String(length=100), nullable=False),
    sa.Column('legal_name', sa.String(length=100), nullable=False),
    sa.Column('short_description', sa.String(length=400), nullable=True),
    sa.Column('long_description', sa.TEXT(), nullable=True),
    sa.Column('status', sa.Enum('ACTIVE', 'INACTIVE', name='brand_status'), nullable=True),
    sa.Column('logo_path', sa.Text(), nullable=False),
    sa.Column('color', sa.String(length=20), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('code'),
    sa.UniqueConstraint('display_name'),
    sa.UniqueConstraint('legal_name'),
    sa.UniqueConstraint('logo_path'),
    sa.UniqueConstraint('name')
    )
    op.create_table('property_brand',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('property_id', sa.String(), nullable=False),
    sa.Column('brand_id', sa.INTEGER(), nullable=False),
    sa.Column('status', sa.Enum('ACTIVE', 'INACTIVE', name='property_brand_status'), nullable=True),
    sa.ForeignKeyConstraint(['brand_id'], ['brand.id'], ),
    sa.ForeignKeyConstraint(['property_id'], ['property.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('provider_brand',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('provider_id', sa.INTEGER(), nullable=False),
    sa.Column('brand_id', sa.INTEGER(), nullable=False),
    sa.Column('status', sa.Enum('ACTIVE', 'INACTIVE', name='provider_brand_status'), nullable=True),
    sa.ForeignKeyConstraint(['brand_id'], ['brand.id'], ),
    sa.ForeignKeyConstraint(['provider_id'], ['provider.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('provider_brand')
    op.drop_table('property_brand')
    op.drop_table('brand')
    # ### end Alembic commands ###
