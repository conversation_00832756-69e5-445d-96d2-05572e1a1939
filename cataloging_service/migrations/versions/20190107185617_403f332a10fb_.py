"""empty message

Revision ID: 403f332a10fb
Revises: 2a3cf58c8848
Create Date: 2019-01-07 18:56:17.101354

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = '403f332a10fb'
down_revision = '2a3cf58c8848'
branch_labels = None
depends_on = None

sku_type_enum_name = 'sku_choice'
tax_type_enum_name = 'tax_choice'

sku_type_options = ('sku', 'bundle')
tax_type_options = ('unit', 'composite', 'derived')

sku_type = sa.Enum(*sku_type_options, name=sku_type_enum_name)
tax_type = sa.Enum(*tax_type_options, name=tax_type_enum_name)


def upgrade():
    sku_type.create(op.get_bind())
    tax_type.create(op.get_bind())
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('sku', sa.Column('sku_type', sa.Enum('sku', 'bundle', name='sku_choice'), nullable=True))
    op.add_column('sku',
                  sa.Column('tax_type', sa.Enum('unit', 'composite', 'derived', name='tax_choice'), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('sku', 'tax_type')
    op.drop_column('sku', 'sku_type')
    # ### end Alembic commands ###
