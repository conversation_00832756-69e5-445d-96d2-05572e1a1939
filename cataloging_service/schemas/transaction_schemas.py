"""
Pydantic schemas for transaction management and payment methods
Provides request/response validation for transaction master, payment methods,
and property onboarding workflows.
"""

from datetime import datetime
from enum import Enum
from typing import Optional, List, Dict, Any

# Import ApiBaseModel for automatic plugin registration
from apispec_pydantic_plugin.models import ApiBaseModel
from pydantic import Field, ConfigDict, field_validator
from pydantic.types import PositiveInt, NonNegativeInt

# Import the swag_schema decorator for OpenAPI registration
from cataloging_service.common.schema_registry import swag_schema


class BaseTransactionSchema(
    ApiBaseModel
):  # Inherit from ApiBaseModel for automatic plugin registration
    """Base schema for transaction-related entities"""

    model_config = ConfigDict(
        from_attributes=True,
        validate_assignment=True,
        arbitrary_types_allowed=True,
        str_strip_whitespace=True,
    )


# ============================================================================
# Transaction Management Schemas
# ============================================================================


class TransactionTypeEnum(str, Enum):
    """Enumeration of transaction types"""

    SKU_SALE = "SKU_SALE"
    SKU_TAX = "SKU_TAX"
    SKU_ALLOWANCE = "SKU_ALLOWANCE"
    SKU_ALLOWANCE_TAX = "SKU_ALLOWANCE_TAX"
    CGST = "CGST"
    SGST = "SGST"
    IGST = "IGST"
    CESS = "CESS"


class EntityTypeEnum(str, Enum):
    """Enumeration of entity types"""

    FRANCHISER = "FRANCHISER"
    HOTEL = "HOTEL"


class TransactionStatusEnum(str, Enum):
    """Enumeration of transaction statuses"""

    ACTIVE = "ACTIVE"
    INACTIVE = "INACTIVE"
    PENDING = "PENDING"
    SYNCED = "SYNCED"


@swag_schema
class TransactionMasterCreateSchema(BaseTransactionSchema):
    """Schema for creating transaction master records"""

    transaction_code: str = Field(
        ..., min_length=3, max_length=100, description="Unique transaction code"
    )
    name: str = Field(..., min_length=1, max_length=255, description="Transaction name")
    property_id: str = Field(
        ..., min_length=1, max_length=50, description="Property ID"
    )
    entity_type: EntityTypeEnum = Field(
        ..., description="Entity type (FRANCHISER or HOTEL)"
    )
    transaction_type: TransactionTypeEnum = Field(
        ..., description="Type of transaction"
    )
    transaction_id: str = Field(
        ...,
        max_length=100,
        description="Triggering transaction ID (sku_id, payment_id)",
    )
    operational_unit_id: str = Field(
        ..., max_length=100, description="Operational unit identifier"
    )
    operational_unit_type: str = Field(
        ..., max_length=50, description="Type of operational unit"
    )
    source: str = Field(..., max_length=100, description="Source system or module")
    gl_code: Optional[str] = Field(
        None, max_length=50, description="General Ledger code"
    )
    erp_id: Optional[str] = Field(
        None, max_length=100, description="ERP system identifier"
    )
    is_merge: bool = Field(False, description="Whether this is a merge transaction")
    particulars: Optional[str] = Field(
        None, description="Transaction particulars/description"
    )
    status: TransactionStatusEnum = Field(
        TransactionStatusEnum.ACTIVE, description="Transaction status"
    )
    transaction_details: Optional[Dict[str, Any]] = Field(
        default_factory=dict, description="Transaction details JSON"
    )

    @field_validator("transaction_code")
    @classmethod
    def validate_transaction_code(cls, v: str) -> str:
        if not v.replace("_", "").replace("-", "").replace(".", "").isalnum():
            raise ValueError(
                "Transaction code must contain only alphanumeric characters, hyphens, underscores, and dots"
            )
        return v.upper()


@swag_schema
class TransactionMasterUpdateSchema(BaseTransactionSchema):
    """Schema for updating transaction master records"""

    name: Optional[str] = Field(None, min_length=1, max_length=255)
    gl_code: Optional[str] = Field(None, max_length=50)
    erp_id: Optional[str] = Field(None, max_length=100)
    is_merge: Optional[bool] = None
    particulars: Optional[str] = None
    status: Optional[TransactionStatusEnum] = None
    transaction_details: Optional[Dict[str, Any]] = None


@swag_schema
class TransactionMasterResponseSchema(BaseTransactionSchema):
    """Schema for transaction master responses"""

    id: PositiveInt
    transaction_code: str
    name: str
    property_id: str
    entity_type: EntityTypeEnum
    transaction_type: TransactionTypeEnum
    transaction_id: str
    operational_unit_id: str
    operational_unit_type: str
    source: str
    gl_code: Optional[str] = None
    erp_id: Optional[str] = None
    is_merge: bool
    particulars: Optional[str] = None
    status: TransactionStatusEnum
    transaction_details: Dict[str, Any]
    created_at: datetime
    modified_at: datetime


@swag_schema
class TransactionDefaultMappingCreateSchema(BaseTransactionSchema):
    """Schema for creating brand-level transaction default mappings"""

    brand_id: PositiveInt = Field(..., description="Brand ID this mapping belongs to")
    transaction_type: TransactionTypeEnum = Field(..., description="Transaction type")
    transaction_type_code: str = Field(
        ..., max_length=50, description="Specific transaction type code"
    )
    entity_type: EntityTypeEnum = Field(
        ..., description="Entity type (FRANCHISER or HOTEL)"
    )
    default_gl_code: Optional[str] = Field(
        None, max_length=50, description="Default GL code"
    )
    default_erp_id: Optional[str] = Field(
        None, max_length=100, description="Default ERP ID"
    )
    default_particulars: Optional[str] = Field(None, description="Default particulars")
    default_is_merge: bool = Field(False, description="Default merge flag")
    transaction_details: Optional[Dict[str, Any]] = Field(
        default_factory=dict, description="Default transaction details JSON"
    )
    is_active: bool = Field(True, description="Whether mapping is active")


@swag_schema
class TransactionDefaultMappingResponseSchema(BaseTransactionSchema):
    """Schema for transaction default mapping responses"""

    id: PositiveInt
    brand_id: PositiveInt
    transaction_type: TransactionTypeEnum
    transaction_type_code: str
    entity_type: EntityTypeEnum
    default_gl_code: Optional[str] = None
    default_erp_id: Optional[str] = None
    default_particulars: Optional[str] = None
    default_is_merge: bool
    transaction_details: Dict[str, Any]
    is_active: bool
    created_at: datetime
    modified_at: datetime


# ============================================================================
# Payment Method Schemas
# ============================================================================


class PaymentMethodTypeEnum(str, Enum):
    """Enumeration of payment method types"""

    CASH = "CASH"
    CARD = "CARD"
    UPI = "UPI"
    WALLET = "WALLET"
    BANK_TRANSFER = "BANK_TRANSFER"
    CHEQUE = "CHEQUE"
    CREDIT = "CREDIT"
    OTHER = "OTHER"


class PaidToEnum(str, Enum):
    """Enumeration of payment recipients"""

    HOTEL = "HOTEL"
    FRANCHISER = "FRANCHISER"
    THIRD_PARTY = "THIRD_PARTY"


class AllowedPaidByEnum(str, Enum):
    """Enumeration of allowed payers"""

    GUEST = "GUEST"
    CORPORATE = "CORPORATE"
    AGENT = "AGENT"
    WALK_IN = "WALK_IN"
    ALL = "ALL"


class PaymentFlowDirectionEnum(str, Enum):
    """Enumeration of payment flow directions"""

    INFLOW = "INFLOW"  # Payment received (money coming in)
    OUTFLOW = "OUTFLOW"  # Refund given (money going out)


@swag_schema
class PaymentMethodCreateSchema(BaseTransactionSchema):
    """Schema for creating tenant-level payment methods"""

    name: str = Field(
        ..., min_length=1, max_length=255, description="Payment method name"
    )
    code: str = Field(
        ..., min_length=2, max_length=50, description="Unique payment method code"
    )
    payment_method_type: PaymentMethodTypeEnum = Field(
        ..., description="Type of payment method"
    )
    paid_to: PaidToEnum = Field(..., description="Who receives the payment")
    allowed_paid_by: AllowedPaidByEnum = Field(
        AllowedPaidByEnum.ALL, description="Who can use this payment method"
    )
    flow_direction: PaymentFlowDirectionEnum = Field(
        PaymentFlowDirectionEnum.INFLOW,
        description="Payment flow direction (INFLOW for payments, OUTFLOW for refunds)",
    )
    config: Optional[Dict[str, Any]] = Field(
        default_factory=dict, description="Payment method configuration"
    )
    is_active: bool = Field(True, description="Whether payment method is active")

    @field_validator("code")
    @classmethod
    def validate_code(cls, v: str) -> str:
        if not v.replace("_", "").replace("-", "").isalnum():
            raise ValueError(
                "Code must contain only alphanumeric characters, hyphens, and underscores"
            )
        return v.upper()


@swag_schema
class PaymentMethodUpdateSchema(BaseTransactionSchema):
    """Schema for updating tenant-level payment methods"""

    name: Optional[str] = Field(None, min_length=1, max_length=255)
    payment_method_type: Optional[PaymentMethodTypeEnum] = None
    paid_to: Optional[PaidToEnum] = None
    allowed_paid_by: Optional[AllowedPaidByEnum] = None
    flow_direction: Optional[PaymentFlowDirectionEnum] = Field(
        None,
        description="Payment flow direction (INFLOW for payments, OUTFLOW for refunds)",
    )
    config: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None


@swag_schema
class PaymentMethodResponseSchema(BaseTransactionSchema):
    """Schema for payment method responses"""

    id: PositiveInt
    name: str
    code: str
    payment_method_type: PaymentMethodTypeEnum
    paid_to: PaidToEnum
    allowed_paid_by: AllowedPaidByEnum
    flow_direction: PaymentFlowDirectionEnum
    config: Dict[str, Any]
    is_active: bool
    created_at: datetime
    modified_at: datetime


# ============================================================================
# Property Onboarding Schemas
# ============================================================================


@swag_schema
class PropertyOnboardingRequestSchema(BaseTransactionSchema):
    """Schema for property onboarding requests"""

    property_id: str = Field(
        ..., min_length=1, max_length=50, description="Property ID"
    )
    brand_id: PositiveInt = Field(..., description="Brand ID for template resolution")
    auto_create_departments: bool = Field(
        True, description="Auto-create departments from templates"
    )
    auto_create_profit_centers: bool = Field(
        True, description="Auto-create profit centers from templates"
    )
    auto_create_skus: bool = Field(True, description="Auto-create SKUs from templates")
    generate_transaction_codes: bool = Field(
        True, description="Generate transaction codes"
    )
    custom_config: Optional[Dict[str, Any]] = Field(
        default_factory=dict, description="Custom configuration overrides"
    )


@swag_schema
class PropertyOnboardingResponseSchema(BaseTransactionSchema):
    """Schema for property onboarding responses"""

    property_id: str
    brand_id: PositiveInt
    departments_created: NonNegativeInt
    profit_centers_created: NonNegativeInt
    skus_created: NonNegativeInt
    transaction_codes_generated: NonNegativeInt
    onboarding_status: str
    created_entities: Dict[str, List[str]]
    errors: List[str]
    warnings: List[str]
    onboarded_at: datetime
