import csv

from flask_script import Command, Option


from cataloging_service.models import GlobalPolicy
from cataloging_service.infrastructure.repositories import repo_provider


class MigrateGlobalPolicyCommand(Command):

    option_list = (Option('--file_path', dest='file_path', required=True,
                          help='The CSV file to read occupancy data from'),)

    def run(self, file_path):
        self.migrate_global_policy(file_path)

    def migrate_global_policy(self, file_path):
        with open(file_path) as global_policy_csv:
            reader = csv.DictReader(global_policy_csv, delimiter='|')
            property_repository = repo_provider.property_repository
            for row in reader:
                global_policy = GlobalPolicy()
                global_policy.id = row['id']
                global_policy.created_at = row['created_at']
                global_policy.modified_at = row['modified_at']
                global_policy.policy_type = row['policy_type']
                global_policy.title = row['title']
                global_policy.description = row['description']
                global_policy.display_in_need_to_know = row['display_in_need_to_know']
                global_policy.display_in_policy = row['display_in_policy']
                property_repository.persist(global_policy)
            property_repository.session().commit()

