import datetime
import logging
import re

from treebo_commons.utils import dateutils
from flask_script import Command, Option

from cataloging_service.client import client_provider
from cataloging_service.constants import migration_row_numbers, error_codes
from cataloging_service.constants.constants import DEFAULT_COST_CENTER_ID_BRAND_CODE
from cataloging_service.constants.model_choices import ParkingChoices, SwimmingPoolChoices, PropertyChoices
from cataloging_service.domain import service_provider
from cataloging_service.domain.property_service import PropertyService
from cataloging_service.exceptions import CatalogingServiceException
from cataloging_service.infrastructure.decorators import atomic_operation
from cataloging_service.infrastructure.repositories import repo_provider, TenantConfigRepository
from cataloging_service.models import Property, Room, Location, PropertyDetail, BankDetail, Owner, Ownership, \
    GuestFacingProcess, RoomTypeConfiguration, PropertyAmenity, AmenityPublicWashroom, AmenityElevator, AmenityParking, \
    AmenityDisableFriendly, AmenitySwimmingPool, AmenityGym, AmenitySpa, AmenityLaundry, AmenityBreakfast, \
    AmenityPayment, AmenityPrivateCab, Bar, Restaurant, BanquetHall, RoomAmenity, AmenityIntercom, AmenityHotWater, \
    AmenityTV, AmenityHeater, AmenityAC, AmenityStove, AmenityTwinBed, Description, NeighbouringPlace, Landmark, \
    TransportStation, TransportStationProperty, RatePlanConfiguration, PropertyLandmark, Brand
from cataloging_service.utils import Utils

logger = logging.getLogger(__name__)


class MigratePropertyCommand(Command):
    option_list = (Option('--sheet_names', dest='sheet_names', required=True,
                          help='Provides a comma separated list of migration sheet names'),
                   Option('--old_hotel', dest='old_hotel', default=False,
                          help='Are you migrating an existing hotel to the system?', action='store_true'),
                   )

    DATE_FORMAT = '%d-%m-%Y'
    TIME_FORMAT = '%H:%M'
    TRUE = ['TRUE', 'YES']

    def __init__(self):
        super(MigratePropertyCommand, self).__init__()
        # Sheet URL https://docs.google.com/spreadsheets/d/1L05RH9DEtuFBWe8OZb4fown7KceaBt25Tcwo4t6kYC0/edit#gid=0
        self.migration_sheet_id = '1qpCLkinjRRE-ddV0LURCJSj23H0SqpJmNQdBVE4nKfs'
        self.sheet_client = client_provider.google_sheets_client
        self.property_repository = repo_provider.property_repository
        self.location_repository = repo_provider.property_location_repository
        self.meta_repository = repo_provider.meta_repository
        self.owner_repository = repo_provider.owner_repository
        self.rows = None
        self.property_service = service_provider.property_service

    def run(self, sheet_names, old_hotel):
        sheets = sheet_names.split(',')
        failed_sheets = []
        for sheet in sheets:
            try:
                self.read_sheet(sheet)
                self.start_migration(old_hotel)
            except Exception as exception:
                logger.error('%s migration failed. Reason: %s' % (sheet, repr(exception)))
                failed_sheets.append(sheet)

        logger.error('Failed sheets:%s' % failed_sheets)

    def migrate(self, sheet_name, old_hotel):
        try:
            self.read_sheet(sheet_name)
            return self.start_migration(old_hotel)
        except Exception as exception:
            logger.error('%s migration failed. Reason: %s' % (sheet_name, repr(exception)))
            raise exception

    @atomic_operation
    def start_migration(self, old_hotel):
        try:
            property_object, location = self.create_property_with_location(old_hotel)
            if not old_hotel and (
                    property_object.status in (PropertyChoices.STATUS_LIVE, PropertyChoices.STATUS_SIGNED)):
                raise CatalogingServiceException(error_codes.STATUS_NOT_PERMITTED)
        except Exception as exception:
            logger.exception(exception)
            raise Exception('Error in Property Info')

        try:
            property_details = self.create_property_details(property_object)
            self.create_bank_details(property_details)
        except Exception as exception:
            logger.exception(exception)
            raise Exception('Error while creating Property Details/Bank Details')

        try:
            self.create_room_config(property_object)
        except Exception as exception:
            logger.exception(exception)
            raise Exception('Error while creating room Configs')

        try:
            self.create_rooms(property_object)
        except Exception as exception:
            logger.exception(exception)
            raise Exception('Error while creating Rooms')

        try:
            self.create_rate_config(property_object)
        except Exception as exception:
            logger.exception(exception)
            raise Exception('Error while creating rate config')

        try:
            self.create_owner_details(property_object)
        except Exception as exception:
            logger.exception(exception)
            raise Exception('Error while creating Owner Details')

        try:
            self.create_guest_facing_details(property_object)
        except Exception as exception:
            logger.exception(exception)
            raise Exception('Exception while creating Guest Facing Details')

        try:
            self.add_guest_types(property_object)
        except Exception as exception:
            logger.exception(exception)
            raise Exception('Exception while adding guest types')

        try:
            self.create_property_amenities(property_object)
        except Exception as exception:
            logger.exception(exception)
            raise Exception('Error while creating property amenities')

        try:
            self.create_bar(property_object)
        except Exception as exception:
            logger.exception(exception)
            raise Exception('Error while creating Bar')

        try:
            self.create_restaurant(property_object)
        except Exception as exception:
            logger.exception(exception)
            raise Exception('Error while creating restaurant')

        try:
            self.create_banquet_hall(property_object)
        except Exception as exception:
            logger.exception(exception)
            raise Exception('Error while creating hall')

        try:
            self.create_room_amenity(property_object)
        except Exception as exception:
            logger.exception(exception)
            raise Exception('Error while creating room amenities')
        try:
            self.create_description(property_object)
        except Exception as exception:
            logger.exception(exception)
            raise Exception('Error while creating description')

        try:
            self.create_neighbourhood(property_object)
        except Exception as exception:
            logger.exception(exception)
            raise Exception('Error while creating neighbourhood')

        try:
            self.create_landmark(property_object)
        except Exception as exception:
            logger.exception(exception)
            raise Exception('Error while creating Landmark')

        try:
            self.create_transport_stations(property_object)
        except Exception as exception:
            logger.exception(exception)
            raise Exception('Error while creating transport station')

        self.property_service.set_amenity_summary(property_object)

        logger.info('Successfully created property with id %s' % property_object.id)
        return property_object

    def create_transport_stations(self, property_object):
        type_row = self.read_row(migration_row_numbers.STATION_TYPE)
        name_row = self.read_row(migration_row_numbers.STATION_NAME)
        latitude_row = self.read_row(migration_row_numbers.STATION_LATITUDE)
        longitude_row = self.read_row(migration_row_numbers.STATION_LONGITUDE)
        distance_row = self.read_row(migration_row_numbers.STATION_DISTANCE)
        direction_row = self.read_row(migration_row_numbers.STATION_DIRECTION)
        hatchback_fare_row = self.read_row(migration_row_numbers.STATION_HATCHBACK_FARE)
        sedan_fare_row = self.read_row(migration_row_numbers.STATION_SEDAN_FARE)

        highest_length = max(len(type_row), len(name_row), len(latitude_row), len(longitude_row), len(distance_row),
                             len(direction_row), len(hatchback_fare_row), len(sedan_fare_row))
        self.normalize_group_row_length(highest_length, type_row, name_row, latitude_row, longitude_row, distance_row,
                                        direction_row, hatchback_fare_row, sedan_fare_row)

        stations = []
        # noinspection PyArgumentList
        for type, name, latitude, longitude, distance, direction, hatchback, sedan in zip(type_row, name_row,
                                                                                          latitude_row, longitude_row,
                                                                                          distance_row, direction_row,
                                                                                          hatchback_fare_row,
                                                                                          sedan_fare_row):
            station = self.property_repository.get_transport_station(type.upper(), name)
            if not station:
                station = TransportStation()
                station.type = self.read_enum(type)
                station.name = self.read_unique(name)
                station.latitude = self.read_number(latitude)
                station.longitude = self.read_number(longitude)
                self.property_repository.persist(station)

            station_property = TransportStationProperty()
            station_property.sedan_cab_fare = self.read_number(sedan)
            station_property.hatchback_cab_fare = self.read_number(hatchback)
            station_property.property_direction = direction
            station_property.distance_from_property = distance
            station_property.property_id = property_object.id
            station_property.transport_station_id = station.id
            self.property_repository.persist(station_property)

            stations.append(station)
        return stations

    def create_landmark(self, property_object):
        type_row = self.read_row(migration_row_numbers.LANDMARK_TYPE)
        name_row = self.read_row(migration_row_numbers.LANDMARK_NAME)
        latitude_row = self.read_row(migration_row_numbers.LANDMARK_LATITUDE)
        longitude_row = self.read_row(migration_row_numbers.LANDMARK_LONGITUDE)
        distance_row = self.read_row(migration_row_numbers.LANDMARK_DISTANCE)
        direction_row = self.read_row(migration_row_numbers.LANDMARK_DIRECTION)
        hatchback_fare_row = self.read_row(migration_row_numbers.LANDMARK_HATCHBACK_CAB_FARE)
        sedan_fare_row = self.read_row(migration_row_numbers.LANDMARK_SEDAN_CAB_FARE)

        highest_length = max(len(type_row), len(name_row), len(latitude_row), len(longitude_row), len(distance_row),
                             len(direction_row), len(hatchback_fare_row), len(sedan_fare_row))
        self.normalize_group_row_length(highest_length, type_row, name_row, latitude_row, longitude_row, distance_row,
                                        direction_row, hatchback_fare_row, sedan_fare_row)

        # noinspection PyArgumentList
        for type, name, latitude, longitude, distance, direction, hatchback, sedan in zip(type_row, name_row,
                                                                                          latitude_row, longitude_row,
                                                                                          distance_row, direction_row,
                                                                                          hatchback_fare_row,
                                                                                          sedan_fare_row):
            latitude = self.read_number(latitude)
            longitude = self.read_number(longitude)

            if not (name and latitude and longitude and type):
                raise Exception('Landmark needs name, latitude, longitude and type')

            landmarks = self.property_repository.get_landmarks(latitude=latitude, longitude=longitude)
            landmark = None

            for possible_landmark in landmarks:
                if Utils.strip_extra_whitespace(name).lower() == Utils.strip_extra_whitespace(
                        possible_landmark.name).lower():
                    landmark = possible_landmark

            if not landmark:
                landmark = Landmark()
                landmark.name = name
                landmark.latitude = latitude
                landmark.longitude = longitude
                self.property_repository.persist(landmark)

            property_landmark = PropertyLandmark()
            property_landmark.property_id = property_object.id
            property_landmark.landmark_id = landmark.id
            property_landmark.type = type
            property_landmark.distance_from_property = self.read_number(distance)
            property_landmark.property_direction = direction
            property_landmark.hatchback_cab_fare = self.read_number(hatchback)
            property_landmark.sedan_cab_fare = self.read_number(sedan)
            self.property_repository.persist(property_landmark)

    def create_neighbourhood(self, property_object):
        neighbourhood = NeighbouringPlace()
        neighbourhood_row = self.read_row(migration_row_numbers.NEIGHBOURHOOD, 9)

        neighbourhood.property_id = property_object.id
        neighbourhood.nearest_hospital = neighbourhood_row[0]
        neighbourhood.utility_shops = neighbourhood_row[1]
        neighbourhood.restaurants = neighbourhood_row[2]
        neighbourhood.tourist_spots = neighbourhood_row[3]
        neighbourhood.corporate_offices = neighbourhood_row[4]
        neighbourhood.popular_malls = neighbourhood_row[5]
        neighbourhood.shopping_streets = neighbourhood_row[6]
        neighbourhood.city_centre = neighbourhood_row[7]

        return self.property_repository.persist(neighbourhood)

    def create_description(self, property_object):
        description = Description()
        description_row = self.read_row(migration_row_numbers.DESCRIPTION, 9)

        description.property_description = description_row[0]
        description.acacia_description = description_row[1]
        description.oak_description = description_row[2]
        description.maple_description = description_row[3]
        description.mahogany_description = description_row[4]
        description.trilight_one = description_row[5]
        description.trilight_two = description_row[6]
        description.trilight_three = description_row[7]
        description.property_id = property_object.id

        return self.property_repository.persist(description)

    def create_room_amenity(self, property_object):
        rooms = self.property_repository.get_property_rooms(property_object.id)

        intercom_row = self.read_row(migration_row_numbers.INTERCOM, 3)
        hot_water_row = self.read_row(migration_row_numbers.HOT_WATER, 6)
        tv_row = self.read_row(migration_row_numbers.TV, 6)
        room_lock_row = self.read_row(migration_row_numbers.ROOM_LOCK, 3)
        toiletteries_row = self.read_row(migration_row_numbers.TOILETTERIES, 2)
        bucket_mug_row = self.read_row(migration_row_numbers.BUCKET, 3)
        mosquitto_repellant_row = self.read_row(migration_row_numbers.MOSQUITO_REPELLANT, 3)
        heater_row = self.read_row(migration_row_numbers.ROOM_HEATER, 4)
        ac_row = self.read_row(migration_row_numbers.ROOM_AC, 3)
        stove_row = self.read_row(migration_row_numbers.ROOM_STOVE, 4)
        fridge_row = self.read_row(migration_row_numbers.ROOM_FRIDGE, 2)
        balcony_row = self.read_row(migration_row_numbers.BALCONY, 2)
        kitchenette_row = self.read_row(migration_row_numbers.KITCHENETTE, 2)
        kitchenette_utensils_row = self.read_row(migration_row_numbers.KITCHENETTE_UTENSILS, 2)
        king_bed_row = self.read_row(migration_row_numbers.KING_BED, 2)
        queen_bed_row = self.read_row(migration_row_numbers.QUEEN_BED, 2)
        joinable_twin_bed_row = self.read_row(migration_row_numbers.JOINABLE_TWIN_BED, 2)
        non_joinable_twin_bed_row = self.read_row(migration_row_numbers.NON_JOINABLE_TWIN_BED, 2)
        single_bed_row = self.read_row(migration_row_numbers.SINGLE_BED, 2)
        wardrobe_row = self.read_row(migration_row_numbers.WARDROBE, 2)
        locker_row = self.read_row(migration_row_numbers.LOCKER, 2)
        microwave_row = self.read_row(migration_row_numbers.MICROWAVE, 2)
        luggage_shelf_row = self.read_row(migration_row_numbers.LUGGAGE_SHELF, 2)
        study_table_chair_row = self.read_row(migration_row_numbers.STUDY_TABLE_CHAIR, 2)
        sofa_chair_row = self.read_row(migration_row_numbers.SOFA_CHAIR, 2)
        coffee_table_row = self.read_row(migration_row_numbers.COFFEE_TABLE, 2)
        other_furniture_row = self.read_row(migration_row_numbers.OTHER_FURNITURE, 2)
        smoke_alarm_row = self.read_row(migration_row_numbers.ROOM_SMOKE_ALARM, 2)
        bath_tub_row = self.read_row(migration_row_numbers.BATH_TUB, 2)
        shower_curtain_row = self.read_row(migration_row_numbers.SHOWER_CURTAIN, 2)
        windows_row = self.read_row(migration_row_numbers.ROOM_WINDOWS, 2)
        smoking_room_row = self.read_row(migration_row_numbers.SMOKING_ROOM, 2)
        shower_cabinet_row = self.read_row(migration_row_numbers.SHOWER_CABINET, 2)
        living_room_row = self.read_row(migration_row_numbers.LIVING_ROOM, 2)
        dining_table_row = self.read_row(migration_row_numbers.DINING_ROOM, 2)
        fan_row = self.read_row(migration_row_numbers.ROOM_FAN, 3)
        amenities = []
        for room in rooms:
            amenity = RoomAmenity()
            amenity.room_id = room.id

            if self.read_boolean(intercom_row[0]):
                amenity.intercom_id = self.create_intercom(intercom_row).id
            if self.read_boolean(hot_water_row[0]):
                amenity.hot_water_id = self.crete_hot_water(hot_water_row).id
            if self.read_boolean(tv_row[0]):
                amenity.tv_id = self.create_tv(tv_row).id
            if self.read_boolean(heater_row[0]):
                amenity.heater_id = self.create_room_heater(heater_row).id
            if self.read_boolean(ac_row[0]):
                amenity.ac_id = self.create_ac(ac_row).id
            if self.read_boolean(stove_row[0]):
                amenity.stove_id = self.create_stove(stove_row).id
            if self.is_room_number_applicable_for_ammenity(room.room_number, joinable_twin_bed_row[0]):
                amenity.twin_bed_id = self.create_twin_bed(True).id
            if self.is_room_number_applicable_for_ammenity(room.room_number, non_joinable_twin_bed_row[0]):
                amenity.twin_bed_id = self.create_twin_bed(False).id

            amenity.lock_type = self.read_enum(room_lock_row[1])
            amenity.fan_type = self.read_enum(fan_row[1])
            amenity.treebo_toiletries = self.read_boolean(toiletteries_row[0])
            amenity.bucket_mug = self.read_enum(bucket_mug_row[1])
            amenity.mosquito_repellent = self.read_enum(mosquitto_repellant_row[1])
            amenity.mini_fridge = self.is_room_number_applicable_for_ammenity(room.room_number, fridge_row[0])
            amenity.balcony = self.is_room_number_applicable_for_ammenity(room.room_number, balcony_row[0])
            amenity.kitchenette = self.is_room_number_applicable_for_ammenity(room.room_number, kitchenette_row[0])
            amenity.kitchenette_utensils = self.is_room_number_applicable_for_ammenity(room.room_number,
                                                                                       kitchenette_utensils_row[0])
            amenity.king_sized_beds = self.is_room_number_applicable_for_ammenity(room.room_number, king_bed_row[0])
            amenity.queen_sized_beds = self.is_room_number_applicable_for_ammenity(room.room_number, queen_bed_row[0])
            amenity.single_beds = self.is_room_number_applicable_for_ammenity(room.room_number, single_bed_row[0])
            amenity.wardrobe = self.is_room_number_applicable_for_ammenity(room.room_number, wardrobe_row[0])
            amenity.locker_available = self.is_room_number_applicable_for_ammenity(room.room_number, locker_row[0])
            amenity.microwave = self.is_room_number_applicable_for_ammenity(room.room_number, microwave_row[0])
            amenity.luggage_shelf = self.is_room_number_applicable_for_ammenity(room.room_number, luggage_shelf_row[0])
            amenity.study_table_chair = self.is_room_number_applicable_for_ammenity(room.room_number,
                                                                                    study_table_chair_row[0])
            amenity.sofa_chair = self.is_room_number_applicable_for_ammenity(room.room_number, sofa_chair_row[0])
            amenity.coffee_table = self.is_room_number_applicable_for_ammenity(room.room_number, coffee_table_row[0])
            amenity.other_furniture = self.is_room_number_applicable_for_ammenity(room.room_number,
                                                                                  other_furniture_row[0])
            amenity.smoke_alarm = self.is_room_number_applicable_for_ammenity(room.room_number, smoke_alarm_row[0])
            amenity.bath_tub = self.is_room_number_applicable_for_ammenity(room.room_number, bath_tub_row[0])
            amenity.shower_curtain = self.is_room_number_applicable_for_ammenity(room.room_number,
                                                                                 shower_curtain_row[0])
            amenity.windows = self.is_room_number_applicable_for_ammenity(room.room_number, windows_row[0])
            amenity.smoking_room = self.is_room_number_applicable_for_ammenity(room.room_number, smoking_room_row[0])
            amenity.shower_cabinets = self.is_room_number_applicable_for_ammenity(room.room_number,
                                                                                  shower_cabinet_row[0])
            amenity.living_room = self.is_room_number_applicable_for_ammenity(room.room_number, living_room_row[0])
            amenity.dining_table = self.is_room_number_applicable_for_ammenity(room.room_number, dining_table_row[0])

            amenities.append(self.property_repository.persist(amenity))

        return amenities

    def is_room_number_applicable_for_ammenity(self, room_number, amenity_rooms):
        if not amenity_rooms:
            return False
        rooms = amenity_rooms.split(',')
        rooms = [room.strip() for room in rooms]

        return room_number.strip() in rooms

    def create_intercom(self, intercom_row):
        intercom = AmenityIntercom()
        intercom.all_rooms_connected = self.read_boolean(intercom_row[1])

        return self.property_repository.persist(intercom)

    def crete_hot_water(self, hot_water_row):
        hot_water = AmenityHotWater()

        hot_water.central_geyser = self.read_boolean(hot_water_row[1])
        hot_water.room_geyser = self.read_boolean(hot_water_row[2])
        hot_water.from_time = self.read_time(hot_water_row[3])
        hot_water.to_time = self.read_time(hot_water_row[4])

        return self.property_repository.persist(hot_water)

    def create_tv(self, tv_row):
        tv = AmenityTV()
        tv.connection_type = self.read_enum(tv_row[1])
        tv.vendor = tv_row[2]
        tv.tv_type = self.read_enum(tv_row[3])
        tv.size = tv_row[4]

        return self.property_repository.persist(tv)

    def create_room_heater(self, heater_row):
        heater = AmenityHeater()
        heater.availability = self.read_enum(heater_row[1])
        heater.charges = heater_row[2]

        return self.property_repository.persist(heater)

    def create_ac(self, ac_row):
        ac = AmenityAC()
        ac.ac_type = self.read_enum(ac_row[1])

        return self.property_repository.persist(ac)

    def create_stove(self, stove_row):
        stove = AmenityStove()
        stove.availability = self.read_enum(stove_row[1])
        stove.stove_type = self.read_enum(stove_row[2])

        return self.property_repository.persist(stove)

    def create_twin_bed(self, joinable):
        bed = AmenityTwinBed()
        bed.joinable = joinable

        return self.property_repository.persist(bed)

    def create_banquet_hall(self, property_object):
        name_row = self.read_row(migration_row_numbers.HALL_NAME)
        floor_row = self.read_row(migration_row_numbers.HALL_FLOOR)
        capacity_row = self.read_row(migration_row_numbers.HALL_CAPACITY)
        size_row = self.read_row(migration_row_numbers.HALL_SIZE)

        highest_length = max(len(name_row), len(floor_row), len(capacity_row), len(size_row))
        self.normalize_group_row_length(highest_length, name_row, floor_row, capacity_row, size_row)

        halls = []
        for name, floor, capacity, size in zip(name_row, floor_row, capacity_row, size_row):
            hall = BanquetHall()
            hall.property_id = property_object.id
            hall.name = name
            hall.floor = self.read_number(floor)
            hall.capacity = self.read_number(capacity)
            hall.size = size
            halls.append(self.property_repository.persist(hall))

        return halls

    def create_restaurant(self, property_object):
        name_row = self.read_row(migration_row_numbers.RESTAURANT_NAME)
        non_veg_row = self.read_row(migration_row_numbers.RESTAURANT_NON_VEG)
        cuisine_row = self.read_row(migration_row_numbers.RESTAURANT_CUISINES)
        open_row = self.read_row(migration_row_numbers.RESTAURANT_OPEN)
        last_order_row = self.read_row(migration_row_numbers.RESTAURANT_LAST_ORDER)
        close_row = self.read_row(migration_row_numbers.RESTAURANT_CLOSE)
        a_la_carte_row = self.read_row(migration_row_numbers.RESTAURANT_A_LA_CARTE)
        buffet_row = self.read_row(migration_row_numbers.RESTAURANT_BUFFET)
        outside_food_row = self.read_row(migration_row_numbers.RESTAURANT_OUTSIDE_FOOD)
        baby_milk_row = self.read_row(migration_row_numbers.RESTAURANT_BABY_MILK)
        washroom_row = self.read_row(migration_row_numbers.RESTAURANT_WASHROOM)
        handwash_row = self.read_row(migration_row_numbers.RESTAURANT_HANDWASH)
        baby_milk_timing_row = self.read_row(migration_row_numbers.RESTAURANT_BABY_MILK_TIMING)
        egg_row = self.read_row(migration_row_numbers.RESTAURANT_EGGS)
        jain_row = self.read_row(migration_row_numbers.RESTAURANT_JAIN_FOOD)
        room_start_row = self.read_row(migration_row_numbers.RESTAURANT_ROOM_START_TIME)
        room_end_row = self.read_row(migration_row_numbers.RESTAURANT_ROOM_END_TIME)

        highest_length = max(len(name_row), len(non_veg_row), len(cuisine_row), len(open_row), len(last_order_row),
                             len(close_row), len(a_la_carte_row), len(buffet_row), len(outside_food_row),
                             len(baby_milk_row), len(washroom_row), len(handwash_row), len(baby_milk_timing_row),
                             len(egg_row), len(jain_row), len(room_start_row), len(room_end_row))
        self.normalize_group_row_length(highest_length, name_row, non_veg_row, cuisine_row, open_row, last_order_row,
                                        close_row, a_la_carte_row, buffet_row, outside_food_row,
                                        baby_milk_row, washroom_row, handwash_row, baby_milk_timing_row, egg_row,
                                        jain_row, room_start_row, room_end_row)

        restaurants = []
        # noinspection PyArgumentList
        for name, non_veg, cuisines, open_time, last_order_time, close_time, a_la_carte, buffet, \
            outside_food, baby_milk, washroom, handwash, \
            milk_timings, eggs, jain, room_start, room_end in zip(name_row, non_veg_row, cuisine_row, open_row,
                                                                  last_order_row, close_row, a_la_carte_row, buffet_row,
                                                                  outside_food_row, baby_milk_row, washroom_row,
                                                                  handwash_row, baby_milk_timing_row, egg_row, jain_row,
                                                                  room_start_row, room_end_row):
            restaurant = Restaurant()
            restaurant.name = name
            restaurant.non_veg = self.read_boolean(non_veg)
            restaurant.open_time = self.read_time(open_time)
            restaurant.last_order_time = self.read_time(last_order_time)
            restaurant.close_time = self.read_time(close_time)
            restaurant.a_la_carte = self.read_boolean(a_la_carte)
            restaurant.buffet = self.read_boolean(buffet)
            restaurant.outside_food_allowed = self.read_boolean(outside_food)
            restaurant.baby_milk_served = self.read_boolean(baby_milk)
            restaurant.washroom_present = self.read_boolean(washroom)
            restaurant.handwash_present = self.read_boolean(handwash)
            restaurant.baby_milk_timing = milk_timings
            restaurant.egg_served = self.read_boolean(eggs)
            restaurant.jain_food_served = self.read_boolean(jain)
            restaurant.room_service_start_time = self.read_time(room_start)
            restaurant.room_service_end_time = self.read_time(room_end)
            restaurant.property_id = property_object.id

            if cuisines:
                for cuisine in cuisines.split(','):
                    restaurant.cuisines.append(self.meta_repository.get_cuisine_by_name(cuisine.strip()))

            restaurants.append(self.property_repository.persist(restaurant))

        return restaurants

    def create_bar(self, property_object):
        name_row = self.read_row(migration_row_numbers.BAR_NAME)
        open_row = self.read_row(migration_row_numbers.BAR_OPEN)
        last_order_row = self.read_row(migration_row_numbers.BAR_LAST_ORDER)
        close_row = self.read_row(migration_row_numbers.BAR_CLOSE)
        room_start_row = self.read_row(migration_row_numbers.BAR_ROOM_START_TIME)
        room_end_row = self.read_row(migration_row_numbers.BAR_ROOM_END_TIME)

        highest_length = max(len(name_row), len(open_row), len(last_order_row), len(close_row), len(room_start_row),
                             len(room_end_row))
        self.normalize_group_row_length(highest_length, name_row, open_row, last_order_row, close_row, room_start_row,
                                        room_end_row)

        bars = []
        # noinspection PyArgumentList
        for name, open_time, last_order, close_time, room_start, room_end in zip(name_row, open_row, last_order_row,
                                                                                 close_row, room_start_row,
                                                                                 room_end_row):
            bar = Bar()
            bar.open_time = self.read_time(open_time)
            bar.last_order_time = self.read_time(last_order)
            bar.close_time = self.read_time(close_time)
            bar.name = name
            bar.room_start_time = self.read_time(room_start)
            bar.room_end_time = self.read_time(room_end)
            bar.property_id = property_object.id

            bars.append(self.property_repository.persist(bar))

        return bars

    def create_property_amenities(self, property_object):
        amenity = PropertyAmenity()
        amenity.property_id = property_object.id

        public_washroom_row = self.read_row(migration_row_numbers.PUBLIC_WASHROOM, 3)
        elevator_row = self.read_row(migration_row_numbers.ELEVATOR, 3)
        ac_row = self.read_row(migration_row_numbers.LOBBY_AC, 2)
        furniture_row = self.read_row(migration_row_numbers.LOBBY_FURNITURE, 2)
        smoke_alarm_row = self.read_row(migration_row_numbers.LOBBY_SMOKE_ALARM, 2)
        security_row = self.read_row(migration_row_numbers.SECURITY_GUARD, 2)
        parking_row = self.read_row(migration_row_numbers.PARKING, 6)
        iron_board_row = self.read_row(migration_row_numbers.IRON_BOARD, 2)
        disability_row = self.read_row(migration_row_numbers.DISABILITY, 5)
        kitchen_row = self.read_row(migration_row_numbers.KITCHEN, 2)
        pool_row = self.read_row(migration_row_numbers.POOL, 7)
        gym_row = self.read_row(migration_row_numbers.GYM, 6)
        spa_row = self.read_row(migration_row_numbers.SPA, 5)
        cloak_row = self.read_row(migration_row_numbers.CLOAK_ROOM, 2)
        driver_row = self.read_row(migration_row_numbers.DRIVER_QUARTERS, 2)
        travel_row = self.read_row(migration_row_numbers.TRAVEL_DESK, 2)
        pets_row = self.read_row(migration_row_numbers.PETS, 2)
        laundry_row = self.read_row(migration_row_numbers.LAUNDRY, 5)
        breakfast_row = self.read_row(migration_row_numbers.BREAKFAST, 7)
        room_service_row = self.read_row(migration_row_numbers.ROOM_SERVICE, 8)
        payment_row = self.read_row(migration_row_numbers.PAYMENT, 3)
        roof_cafe_row = self.read_row(migration_row_numbers.ROOF_CAFE, 2)
        pool_table_row = self.read_row(migration_row_numbers.POOL_TABLE, 3)
        private_cab_row = self.read_row(migration_row_numbers.CAB, 3)

        if self.read_boolean(public_washroom_row[0]):
            amenity.public_washroom = self.create_public_washroom(public_washroom_row)
        if self.read_boolean(elevator_row[0]):
            amenity.elevator = self.create_elevator(elevator_row)
        if self.read_boolean(parking_row[0]):
            amenity.parking = self.create_parking(parking_row)
        if self.read_boolean(pool_row[0]):
            amenity.swimming_pool = self.create_swimming_pool(pool_row)
        if self.read_boolean(gym_row[0]):
            amenity.gym = self.create_gym(gym_row)
        if self.read_boolean(spa_row[0]):
            amenity.spa = self.create_spa(spa_row)
        if self.read_boolean(laundry_row[0]):
            amenity.laundry = self.create_laundry(laundry_row)
        if self.read_boolean(breakfast_row[0]):
            amenity.breakfast = self.create_breakfast(breakfast_row)
        if self.read_boolean(private_cab_row[0]):
            amenity.private_cab = self.create_private_cab(private_cab_row)

        amenity.disable_friendly = self.create_disability(disability_row)
        amenity.payment = self.create_payment(payment_row)
        amenity.lobby_ac = self.read_boolean(ac_row[0])
        amenity.lobby_furniture = self.read_boolean(furniture_row[0])
        amenity.lobby_smoke_alarm = self.read_boolean(smoke_alarm_row[0])
        amenity.security = self.read_boolean(security_row[0])
        amenity.iron_board_count = self.read_number(iron_board_row[0])
        amenity.pantry = self.read_boolean(kitchen_row[0])
        amenity.cloak_room = self.read_boolean(cloak_row[0])
        amenity.driver_quarters_count = self.read_number(driver_row[0])
        amenity.travel_desk = self.read_boolean(travel_row[0])
        amenity.pets_allowed = self.read_boolean(pets_row[0])
        amenity.room_service = self.read_boolean(room_service_row[0])
        amenity.roof_top_cafe = self.read_boolean(roof_cafe_row[0])
        amenity.pool_table = self.read_boolean(pool_table_row[0])

        return self.property_repository.persist(amenity)

    def create_laundry(self, laundry_row):
        laundry = AmenityLaundry()
        laundry.pickup_time = laundry_row[1]
        laundry.drop_time = laundry_row[2]
        laundry.is_external = self.read_boolean(laundry_row[3])

        return self.property_repository.persist(laundry)

    def create_breakfast(self, breakfast_row):
        breakfast = AmenityBreakfast()
        breakfast.type = self.read_enum(breakfast_row[1])
        breakfast.service_area = self.read_enum(breakfast_row[2])
        breakfast.non_veg = self.read_boolean(breakfast_row[3])
        breakfast.rotational = self.read_boolean(breakfast_row[5])

        if breakfast_row[4]:
            for cuisine in breakfast_row[4].split(','):
                breakfast.cuisines.append(self.meta_repository.get_cuisine_by_name(cuisine.strip()))

        return self.property_repository.persist(breakfast)

    def create_payment(self, payment_row):
        payment = AmenityPayment()
        payment.amex_accepted = self.read_boolean(payment_row[0])
        payment.wallet_accepted = self.read_boolean(payment_row[1])

        return self.property_repository.persist(payment)

    def create_private_cab(self, cab_row):
        cab = AmenityPrivateCab()
        cab.charges = cab_row[1]

        return self.property_repository.persist(cab)

    def create_spa(self, spa_row):
        spa = AmenitySpa()
        spa.open_time = self.read_time(spa_row[1])
        spa.close_time = self.read_time(spa_row[2])
        spa.active = self.read_boolean(spa_row[3])

        return self.property_repository.persist(spa)

    def create_gym(self, gym_row):
        gym = AmenityGym()
        gym.open_time = self.read_time(gym_row[1])
        gym.close_time = self.read_time(gym_row[2])
        gym.equipments_available = gym_row[3]
        gym.active = self.read_boolean(gym_row[4])

        return self.property_repository.persist(gym)

    def create_swimming_pool(self, pool_row):
        pool = AmenitySwimmingPool()

        pool.location = SwimmingPoolChoices.LOCATION_OUTDOORS_UNCOVERED
        if self.read_boolean(pool_row[1]):
            pool.location = SwimmingPoolChoices.LOCATION_INDOORS_COVERED
        pool.open_time = self.read_time(pool_row[3])
        pool.close_time = self.read_time(pool_row[4])
        pool.pool_size = pool_row[2]
        pool.active = self.read_boolean(pool_row[5])

        return self.property_repository.persist(pool)

    def create_public_washroom(self, washroom_row):
        washroom = AmenityPublicWashroom()
        washroom.gender_segregated = self.read_boolean(washroom_row[1])

        return self.property_repository.persist(washroom)

    def create_elevator(self, elevator_row):
        elevator = AmenityElevator()
        elevator.floors_accessible = elevator_row[1]

        return self.property_repository.persist(elevator)

    def create_parking(self, parking_row):
        parking = AmenityParking()
        parking.location = ParkingChoices.LOCATION_OUTDOORS_UNCOVERED
        if self.read_boolean(parking_row[1]):
            parking.location = ParkingChoices.LOCATION_INDOORS_BASEMENT

        parking.max_two_wheelers = self.read_number(parking_row[2])
        parking.max_four_wheelers = self.read_number(parking_row[3])
        parking.charges = parking_row[4]

        return self.property_repository.persist(parking)

    def create_disability(self, disability_row):
        disability = AmenityDisableFriendly()
        disability.wheelchair_count = self.read_number(disability_row[0])
        disability.ramp_available = self.read_boolean(disability_row[1])
        disability.disable_friendly_rooms = disability_row[2]
        disability.disable_friendly_room_available = True if disability.disable_friendly_rooms else False

        return self.property_repository.persist(disability)

    def create_rate_config(self, property_object):
        def get_all_plans():
            all_plans = self.property_repository.get_all_rate_plans()
            return [rate_plan.plan for rate_plan in all_plans]

        try:
            rate_plan_row = self.read_row(migration_row_numbers.RATE_PLAN_CONFIGURATIONS)
        except:
            rate_plan_row = get_all_plans()

        if len(rate_plan_row) == 0:
            rate_plan_row = get_all_plans()

        rate_configs = []

        for rate_plan in rate_plan_row:
            rate_config = RatePlanConfiguration()
            rate_config.rate_plan = self.property_repository.get_rate_plan_by_name(rate_plan)
            rate_config.property = property_object
            rate_configs.append(rate_config)

        self.property_repository.persist_all(rate_configs)

    def create_room_config(self, property_object):
        room_type_row = self.read_row(migration_row_numbers.CONFIG_ROOM_TYPE)
        min_occupancy_row = self.read_row(migration_row_numbers.CONFIG_MIN_OCCUPY)
        max_occupancy_row = self.read_row(migration_row_numbers.CONFIG_MAX_OCCUPY)
        extra_bed_row = self.read_row(migration_row_numbers.CONFIG_EXTRA_BED)
        adults_row = self.read_row(migration_row_numbers.CONFIG_ADULTS)
        mm_id_row = self.read_row(migration_row_numbers.CONFIG_MM_ID)
        children_row = self.read_row(migration_row_numbers.CONFIG_CHILDREN)
        max_total_row = self.read_row(migration_row_numbers.CONFIG_MAX_TOTAL)

        highest_length = max(len(room_type_row), len(min_occupancy_row), len(max_occupancy_row), len(extra_bed_row),
                             len(adults_row), len(mm_id_row), len(children_row), len(max_total_row))
        self.normalize_group_row_length(highest_length, room_type_row, min_occupancy_row, max_occupancy_row,
                                        extra_bed_row, adults_row, mm_id_row, children_row, max_total_row)

        room_configs = []
        # noinspection PyArgumentList
        for room_type, min_occupancy, max_occupancy, extra_bed, adults, mm_id, children, max_total in zip(room_type_row,
                                                                                                          min_occupancy_row,
                                                                                                          max_occupancy_row,
                                                                                                          extra_bed_row,
                                                                                                          adults_row,
                                                                                                          mm_id_row,
                                                                                                          children_row,
                                                                                                          max_total_row):
            room_config = RoomTypeConfiguration()
            existing_room_type = self.property_repository.get_room_type_by_name(room_type.upper())
            room_config.room_type_id = existing_room_type.id
            room_config.property_id = property_object.id
            room_config.provider = property_object.property_detail.provider
            room_config.min_occupancy = self.read_number(min_occupancy)
            room_config.max_occupancy = max_occupancy
            room_config.extra_bed = self.read_enum(extra_bed)
            room_config.adults = self.read_number(adults)
            room_config.mm_id = self.read_unique(mm_id)
            room_config.children = self.read_number(children)
            room_config.max_total = self.read_number(max_total)
            room_config.ext_room_code = existing_room_type.code
            room_config.ext_room_name = existing_room_type.type
            room_config.display_name = existing_room_type.type

            string_to_int = lambda x: x if x is None else int(x)

            try:
                service_provider.room_service.validate_room_type_config(string_to_int(room_config.adults),
                                                                        string_to_int(room_config.children),
                                                                        string_to_int(room_config.max_total),
                                                                        string_to_int(room_config.min_occupancy))
            except:
                raise CatalogingServiceException(error_codes.INVALID_OCCUPANCY)
            room_configs.append(self.property_repository.persist(room_config))

        return room_configs

    def add_guest_types(self, property_object):
        suited_to_row = self.read_row(migration_row_numbers.SUITED_TO)

        for suited_to in suited_to_row:
            property_object.guest_types.append(self.meta_repository.get_guest_type_by_name(suited_to))

        return self.meta_repository.persist(property_object)

    def create_guest_facing_details(self, property_object):
        guest_facing_details = GuestFacingProcess()
        guest_facing_details.property_id = property_object.id
        guest_facing_row = self.read_row(migration_row_numbers.GUEST_PROCESS, 7)

        guest_facing_details.checkin_time = self.read_time(guest_facing_row[0])
        guest_facing_details.checkout_time = self.read_time(guest_facing_row[2])
        free_early_checkin = self.read_time(guest_facing_row[1])
        # see if free_early_checkin >= 06:00
        if free_early_checkin < datetime.time(6, 0, 0):
            raise CatalogingServiceException("Early-checkin-time should be greater than or equal to 6am")
        guest_facing_details.free_early_checkin = free_early_checkin
        guest_facing_details.free_late_checkout = self.read_time(guest_facing_row[3])
        guest_facing_details.early_checkin_fee = guest_facing_row[4]
        guest_facing_details.late_checkout_fee = guest_facing_row[5]

        return self.property_repository.persist(guest_facing_details)

    def create_owner_details(self, property_object):
        owner_first_name_row = self.read_row(migration_row_numbers.OWNER_FIRST_NAME)
        owner_middle_name_row = self.read_row(migration_row_numbers.OWNER_MIDDLE_NAME)
        owner_last_name_row = self.read_row(migration_row_numbers.OWNER_LAST_NAME)
        owner_primary_row = self.read_row(migration_row_numbers.OWNER_PRIMARY)
        owner_email_row = self.read_row(migration_row_numbers.OWNER_EMAIL)
        owner_phone_row = self.read_row(migration_row_numbers.OWNER_PHONE)
        owner_occupation_row = self.read_row(migration_row_numbers.OWNER_OCCUPATION)
        owner_education_row = self.read_row(migration_row_numbers.OWNER_EDUCATION)

        highest_length = max(len(owner_first_name_row), len(owner_middle_name_row), len(owner_last_name_row),
                             len(owner_primary_row), len(owner_email_row), len(owner_phone_row),
                             len(owner_occupation_row), len(owner_education_row))
        self.normalize_group_row_length(highest_length, owner_first_name_row, owner_middle_name_row,
                                        owner_last_name_row, owner_primary_row, owner_email_row, owner_phone_row,
                                        owner_occupation_row, owner_education_row)

        owners = []
        # noinspection PyArgumentList
        for first_name, middle_name, last_name, is_primary, email, phone, occupation, education in zip(
                owner_first_name_row, owner_middle_name_row, owner_last_name_row, owner_primary_row, owner_email_row,
                owner_phone_row, owner_occupation_row, owner_education_row):
            owner = self.owner_repository.get_owner(first_name, last_name, email)

            if not owner:
                owner = Owner()
                owner.first_name = first_name
                owner.middle_name = middle_name
                owner.last_name = last_name
                owner.email = email
                owner.phone_number = phone
                owner.occupation = occupation
                owner.education = education

            owners.append(self.property_repository.persist(owner))

            if owner.email and not Utils.is_valid_email(owner.email):
                raise CatalogingServiceException(error_codes.INVALID_EMAIL)

            ownership = Ownership()
            ownership.property_id = property_object.id
            ownership.owner_id = owner.id
            ownership.primary = self.read_boolean(is_primary)

            if ownership.primary and not owner.email:
                raise CatalogingServiceException(error_codes.EMAIL_REQUIRED_FOR_PRIMARY_OWNER)
            if ownership.primary and not owner.phone_number:
                raise CatalogingServiceException(error_codes.PHONE_REQUIRED_FOR_PRIMARY_OWNER)

            self.property_repository.persist(ownership)

        return owners

    def create_bank_details(self, property_detail):
        bank_row = self.read_row(migration_row_numbers.BANK, 7)
        bank = BankDetail()
        bank.account_name = bank_row[0]
        bank.account_number = bank_row[1]
        bank.account_type = self.read_enum(bank_row[2])
        bank.ifsc_code = bank_row[3]
        bank.bank = bank_row[4]
        bank.branch = bank_row[5]
        bank.property_detail = property_detail

        if not Utils.is_valid_ifsc_code(bank.ifsc_code):
            raise CatalogingServiceException(error_codes.INVALID_IFSC_CODE)

        return self.property_repository.persist(bank)

    def create_property_details(self, property_object):
        property_type_details_row = self.read_row(migration_row_numbers.PROPERTY_TYPE_DETAILS, 7)
        reception_row = self.read_row(migration_row_numbers.RECEPTION, 3)
        other_details_row = self.read_row(migration_row_numbers.OTHER_PROPERTY_DETAILS, 7)
        is_leased_row = self.read_row(migration_row_numbers.PROPERTY_LEASED, 2)

        property_details = PropertyDetail()

        property_details.property_id = property_object.id

        property_details.neighbourhood_type = self.read_enum(property_type_details_row[0])
        property_details.property_type = self.read_enum(property_type_details_row[2])
        property_details.property_style = self.read_enum(property_type_details_row[3])
        property_details.building_style = self.read_enum(property_type_details_row[5])
        provider_name = self.read_enum(property_type_details_row[6])
        if not provider_name:
            # no description as of now, as everything is overridden by the default error message
            logger.error("No provider specified")
            raise Exception
        provider = repo_provider.provider_repository.rget_provider_by_name(provider_name)
        if not provider:
            # no description as of now, as everything is overridden by the default error message
            logger.error("No provider found")
            raise Exception
        property_details.provider = provider
        property_details.floor_count = self.read_number(other_details_row[0])
        property_details.star_rating = self.read_number(other_details_row[1])
        property_details.construction_year = self.read_number(other_details_row[3])
        property_details.is_leased = self.read_boolean(is_leased_row[0])

        property_details.neighbourhood_detail = property_type_details_row[1]
        property_details.style_detail = property_type_details_row[4]
        property_details.reception_landline = reception_row[0]
        property_details.reception_mobile = reception_row[1]
        property_details.previous_franchise = self.read_boolean(other_details_row[2])
        property_details.unmarried_couple_allowed = self.read_boolean(other_details_row[4])
        property_details.local_id_allowed = self.read_boolean(other_details_row[5])

        return self.property_repository.persist(property_details)

    def create_rooms(self, property_object):
        room_number_row = self.read_row(migration_row_numbers.ROOM_NUMBER)
        building_number_row = self.read_row(migration_row_numbers.BUILDING_NUMBER)
        floor_row = self.read_row(migration_row_numbers.FLOOR)
        size_row = self.read_row(migration_row_numbers.SIZE)
        type_row = self.read_row(migration_row_numbers.ROOM_TYPE)
        is_active_row = self.read_row(migration_row_numbers.ROOM_ACTIVE)

        highest_length = max(len(room_number_row), len(building_number_row),
                             len(floor_row), len(size_row), len(type_row), len(is_active_row))
        self.normalize_group_row_length(highest_length, room_number_row, building_number_row,
                                        floor_row, size_row, type_row, is_active_row)

        rooms = []

        # noinspection PyArgumentList
        for room_num, building_num, floor_num, size, room_type, active in zip(room_number_row,
                                                                              building_number_row, floor_row,
                                                                              size_row, type_row, is_active_row):
            room_size = self.read_number(size)
            room = Room()
            room.room_number = room_num
            room.building_number = building_num
            room.floor_number = self.read_number(floor_num)
            room.room_size = 0

            try:
                room.room_size = float(room_size)
            except:
                try:
                    size_search = re.search('(\d+\.?\d*)', room_size)
                    if size_search:
                        room.room_size = float(size_search.group(1))
                except:
                    logger.exception('Room size could not be parsed')

            room.is_active = self.read_boolean(active)
            room.property_id = property_object.id
            room.room_type_id = self.property_repository.get_room_type_by_name(room_type.upper()).id

            room_config = repo_provider.property_repository.get_property_room_type_configuration_by_room_type(
                property_object.id, room.room_type_id)
            if not room_config:
                logger.error('Room type config not present')
                raise CatalogingServiceException(error_codes.ROOM_TYPE_CONFIG_NOT_FOUND)

            room.room_type_config = room_config

            rooms.append(self.property_repository.persist(room))

        room_configs = self.property_repository.get_property_room_type_configurations(property_object.id)
        min_size_map = self.property_repository.get_property_min_room_size_map(property_object.id)
        for room_config in room_configs:
            room_config.min_room_size = min_size_map.get(room_config.room_type_id, None)

        self.property_repository.persist_all(room_configs)

        return rooms

    def create_property_with_location(self, old_hotel):
        name_row = self.read_row(migration_row_numbers.NAME, 4)
        brand_row = self.read_row(migration_row_numbers.BRAND, 2)
        hx_id_row = self.read_row(migration_row_numbers.HX_ID, 2)
        status_row = self.read_row(migration_row_numbers.STATUS, 2)
        dates_row = self.read_row(migration_row_numbers.DATES, 5)

        location_row = self.read_row(migration_row_numbers.LOCATION, 11)
        country = self.location_repository.get_country_by_name(location_row[2])
        state = self.location_repository.get_state_by_name(location_row[3], country.id)
        city = self.location_repository.get_city_by_name(location_row[4], state.id)
        locality = self.location_repository.get_locality_by_name(location_row[5], city.id)
        micro_market = self.location_repository.get_micro_market_by_name(location_row[6], city.id)

        property_object = Property()
        property_object.name = self.read_unique(name_row[0])
        property_object.old_name = name_row[1]
        property_object.legal_name = name_row[2]
        property_object.hx_id = self.read_unique(hx_id_row[0])
        property_object.status = self.read_enum(status_row[0])
        property_object.signed_date = self.read_date(dates_row[0])
        property_object.contractual_launch_date = self.read_date(dates_row[1])
        property_object.launched_date = self.read_date(dates_row[2])
        property_object.churned_date = self.read_date(dates_row[3])
        property_object.current_business_date = dateutils.today()
        property_object.region = self.read_unique(location_row[10])
        if not old_hotel:
            PropertyService.validate_and_sanitize_property_dates(property_object)
        brand = PropertyService.get_brand_for_cost_center_id(brand_name=self.read_unique(brand_row[0]))
        brand_code = brand.brand_code if (brand and brand.brand_code) else None
        if not brand_code:
            brand_code = TenantConfigRepository().load_v2(config_name=DEFAULT_COST_CENTER_ID_BRAND_CODE)[0].config_value
        property_object = self.property_service.assign_id_and_save_property(property_object, city, brand_code)

        location = Location()

        location.latitude = self.read_number(location_row[0])
        location.longitude = self.read_number(location_row[1])
        location.city_id = city.id
        location.micro_market_id = micro_market.id
        location.locality_id = locality.id
        location.maps_link = location_row[7]
        location.pincode = self.read_number(location_row[8])
        location.postal_address = location_row[9]
        property_object.region = location_row[10]
        location.property_id = property_object.id

        location = self.location_repository.persist(location)

        return property_object, location

    def read_row(self, row_num, min_columns=None):
        row = self.rows[row_num - 1]
        if min_columns and min_columns > len(row):
            difference = min_columns - len(row)
            row.extend([''] * difference)
        row = self.rows[row_num - 1][1:]
        row = [row_element.strip() for row_element in row]
        row = [None if not row_element else row_element for row_element in row]
        logger.info('Row %s:%s' % (row_num, row))
        return row

    def read_sheet(self, sheet_name):
        self.rows = self.sheet_client.read_rows(self.migration_sheet_id, sheet_name, 1,
                                                migration_row_numbers.RATE_PLAN_CONFIGURATIONS)
        logger.info('Successfully read the sheet %s' % sheet_name)

    def read_date(self, date_string):
        if date_string:
            return Utils.parse_date_time(date_string, self.DATE_FORMAT)
        return None

    def read_time(self, time_string):
        if time_string:
            return Utils.parse_date_time(time_string, self.TIME_FORMAT).time()
        return None

    def read_number(self, number_string):
        if number_string:
            return number_string
        return None

    def read_boolean(self, boolean_string):
        return boolean_string and (boolean_string.upper() in self.TRUE)

    def read_enum(self, enum_string):
        if enum_string:
            return enum_string.upper()
        return None

    def read_unique(self, string):
        if string:
            return string
        return None

    def normalize_group_row_length(self, group_max_length, *args):
        for row in args:
            if len(row) < group_max_length:
                difference = group_max_length - len(row)
                row.extend([None] * difference)

