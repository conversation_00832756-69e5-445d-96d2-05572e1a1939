import csv

from flask_script import Command, Option


from cataloging_service.models import PropertyPolicy
from cataloging_service.infrastructure.repositories import repo_provider


class MigratePropertyPolicyCommand(Command):

    option_list = (Option('--file_path', dest='file_path', required=True,
                          help='The CSV file to read occupancy data from'),)

    def run(self, file_path):
        self.migrate_property_policy(file_path)

    def migrate_property_policy(self, file_path):
        with open(file_path) as property_policy_csv:
            reader = csv.DictReader(property_policy_csv, delimiter='|')
            property_repository = repo_provider.property_repository
            for row in reader:
                property_policy = PropertyPolicy()
                property_policy.id = row['id']
                property_policy.created_at = row['created_at']
                property_policy.modified_at = row['modified_at']
                property_policy.policy_type = row['policy_type']
                property_policy.title = row['title']
                property_policy.description = row['description']
                property_policy.display_in_need_to_know = row['display_in_need_to_know']
                property_policy.display_in_policy = row['display_in_policy']
                property_repository.persist(property_policy)
            property_repository.session().commit()

