import logging

from treebo_commons.utils import dateutils

from flask_script import Option
from flask_script.commands import Command

from cataloging_service.domain import service_provider
from cataloging_service.infrastructure.decorators import atomic_operation
from cataloging_service.infrastructure.repositories import repo_provider

logger = logging.getLogger(__name__)


class PushSkuEvents(Command):
    sku_repository = repo_provider.sku_repository
    option_list = (Option('--start_date', dest='start_date', required=True,
                          help='Provide the start date to push sku events, Format: YYYY-MM-DD'),)

    def run(self, start_date):
        self.push_sku_events(start_date)

    @atomic_operation
    def push_sku_events(self, start_date):
        start_date = dateutils.ymd_str_to_date(start_date)

        skus = self.sku_repository.rget_sku_for_date_range_greater_than(start_date)
        sku_ids = [sku.id for sku in skus]
        print("Publishing sku events for {} skus".format(len(sku_ids)))
        service_provider.sku_service.publish_sku(sku_ids)

        hotel_skus = self.sku_repository.rget_property_sku_for_date_range_greater_than(start_date)
        property_sku_ids = [sku.id for sku in hotel_skus]
        print("Publishing property sku events for {} skus".format(len(property_sku_ids)))
        service_provider.property_service.publish_property_skus(property_sku_ids)
