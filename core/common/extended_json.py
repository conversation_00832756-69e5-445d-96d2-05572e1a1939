import json
from datetime import datetime, date
from decimal import Decimal
from enum import Enum
from functools import singledispatch, partial


@singledispatch
def to_serializable(obj):
    """Used by default."""
    if hasattr(obj, '__json__'):
        return obj.__json__()
    return str(obj)


@to_serializable.register(datetime)
def ts_datetime(obj):
    return obj.isoformat()


@to_serializable.register(date)
def ts_date(obj):
    return obj.isoformat()


@to_serializable.register(Enum)
def ts_enum(obj):
    return obj.value


@to_serializable.register(Decimal)
def ts_decimal(obj):
    return "{obj:.4f}".format(obj=obj)


def patch_json():
    json.dumps = partial(json.dumps, **{'default': to_serializable})
