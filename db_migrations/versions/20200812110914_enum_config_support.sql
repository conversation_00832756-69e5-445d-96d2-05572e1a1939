-- revision: '20200812110914_enum_config_support'
-- down_revision: '20200818143154_iso_field_addition'

-- upgrade

-- Allowed Config Value
CREATE TABLE allowed_config_value (
    id integer NOT NULL,
    config_name character varying NOT NULL,
    value character varying
);

CREATE SEQUENCE allowed_config_value_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER SEQUENCE allowed_config_value_id_seq OWNED BY allowed_config_value.id;
ALTER TABLE ONLY allowed_config_value ALTER COLUMN id SET DEFAULT nextval('allowed_config_value_id_seq'::regclass);
ALTER TABLE ONLY allowed_config_value ADD CONSTRAINT allowed_config_value_pkey PRIMARY KEY (id);

ALTER TABLE ONLY allowed_config_value
    ADD CONSTRAINT allowed_config_value_available_config_fkey FOREIGN KEY (config_name) REFERENCES available_config(name) ON DELETE CASCADE;


-- User Defined Enum
CREATE TABLE user_defined_enum (
    enum_id integer NOT NULL,
    property_id character varying,
    enum_name character varying NOT NULL,
    label character varying,
    role character varying
);

CREATE SEQUENCE user_defined_enum_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER SEQUENCE user_defined_enum_id_seq OWNED BY user_defined_enum.enum_id;
ALTER TABLE user_defined_enum ALTER COLUMN enum_id SET DEFAULT nextval('user_defined_enum_id_seq'::regclass);
ALTER TABLE ONLY user_defined_enum ADD CONSTRAINT user_defined_enum_pkey PRIMARY KEY (enum_id);
ALTER TABLE ONLY user_defined_enum
    ADD CONSTRAINT user_defined_enum_property_fkey FOREIGN KEY (property_id) REFERENCES property(id);


-- User Defined Enum Values
CREATE TABLE user_defined_enum_values (
    enum_value_id integer not null,
    enum_id integer not null,
    value character varying not null,
    label character varying
);

CREATE SEQUENCE user_defined_enum_values_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER SEQUENCE user_defined_enum_values_id_seq OWNED BY user_defined_enum_values.enum_value_id;
ALTER TABLE user_defined_enum_values ALTER COLUMN enum_value_id SET DEFAULT nextval('user_defined_enum_values_id_seq'::regclass);
ALTER TABLE ONLY user_defined_enum_values ADD CONSTRAINT user_defined_enum_values_pkey PRIMARY KEY (enum_value_id);
ALTER TABLE ONLY user_defined_enum_values
    ADD CONSTRAINT user_defined_enum_values_enum_fkey FOREIGN KEY (enum_id) REFERENCES user_defined_enum(enum_id) ON DELETE CASCADE;
ALTER TABLE ONLY user_defined_enum_values
    ADD CONSTRAINT _unique_enum_value_per_enum UNIQUE (enum_id, value);

-- downgrade
DROP TABLE user_defined_enum_values;
DROP TABLE user_defined_enum;
DROP TABLE allowed_config_value;
