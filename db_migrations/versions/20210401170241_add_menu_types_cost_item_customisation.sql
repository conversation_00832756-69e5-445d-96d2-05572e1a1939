-- revision: '20210401170241_add_menu_types_cost_item_customisation'
-- down_revision: '20210323132930_add_unique_index_sku_identifier'

-- upgrade
alter table menu alter column menu_type TYPE character varying;
alter table item_customisation add column cost decimal not null default 0;
alter table item add column is_available boolean default TRUE;
alter table combo add column is_available boolean default TRUE;
alter table item add column is_vegetarian boolean default TRUE;
alter table combo add column is_vegetarian boolean default TRUE;

-- downgrade
ALTER TABLE menu alter COLUMN menu_type type menu_type USING menu_type::menu_type;
alter table item_customisation drop column cost;
alter table item drop column is_available;
alter table combo drop column is_available;
alter table item drop column is_vegetarian;
alter table combo drop column is_vegetarian;