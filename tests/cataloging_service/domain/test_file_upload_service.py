from unittest import TestCase
from unittest.mock import Mock
from unittest.mock import patch

from cataloging_service.client.google_drive_client import GoogleDriveFileResource
from cataloging_service.domain.file_upload_service import FileUploadService
from cataloging_service.infrastructure import decorators
from cataloging_service.models import GoogleDriveFile, Property, GoogleDriveBaseFolder


class TestFileUploadService(TestCase):
    MOCK_UPLOADED_FILE_ID = '1234'
    COMMON_FOLDER_NAME = 'Common'
    MOCK_FOLDER_ID = '4321'
    MOCK_FOLDER_LINK = 'google.com'
    MOCK_FILE_NAME = 'file'
    MOCK_PROPERTY_FOLDER_NAME = 'PROPERTY'
    MOCK_PROPERTY_ID = 1
    MOCK_DOCUMENT_FOLDER_NAME = 'Documents'
    MOCK_IMAGE_FOLDER_NAME = 'Images'

    CONFIG = {'GOOGLE_DRIVE_ROOT_FOLDER_ID': ''}

    def setUp(self):
        self.mock_db = patch.object(decorators, 'db', new=Mock())
        self.mock_db.start()

        self.drive_client = Mock()
        self.drive_repository = Mock()
        self.drive_repository.persist.side_effect = lambda x: x
        self.file_upload_service = FileUploadService(self.drive_client, self.drive_repository)

    def tearDown(self):
        self.mock_db.stop()

    @patch.object(FileUploadService, '_FileUploadService__upload_property_file')
    @patch.object(FileUploadService, '_FileUploadService__upload_common_file')
    def test_save_common_file(self, upload_common_file, upload_property_file):
        mock_file_model = self.get_mock_common_file_model()
        mock_file_data = self.get_mock_file_data()
        mock_uploaded_file = self.get_mock_uploaded_file()

        upload_common_file.return_value = mock_uploaded_file
        upload_property_file.return_value = None

        self.file_upload_service.save_property_file(mock_file_model, mock_file_data)

        self.assertEqual(upload_common_file.call_count, 1)
        self.assertEqual(upload_property_file.call_count, 0)
        upload_common_file.assert_called_with(mock_file_model, mock_file_data)
        self.assertEqual(mock_file_model.file_id, self.MOCK_UPLOADED_FILE_ID)

    @patch.object(FileUploadService, '_FileUploadService__upload_property_file')
    @patch.object(FileUploadService, '_FileUploadService__upload_common_file')
    def test_save_property_file(self, upload_common_file, upload_property_file):
        mock_file_model = self.get_mock_property_file_model()
        mock_file_data = self.get_mock_file_data()
        mock_uploaded_file = self.get_mock_uploaded_file()

        upload_common_file.return_value = None
        upload_property_file.return_value = mock_uploaded_file

        self.file_upload_service.save_property_file(mock_file_model, mock_file_data)

        self.assertEqual(upload_common_file.call_count, 0)
        self.assertEqual(upload_property_file.call_count, 1)
        upload_property_file.assert_called_with(mock_file_model, mock_file_data)
        self.assertEqual(mock_file_model.file_id, self.MOCK_UPLOADED_FILE_ID)

    def test_file_deletion(self):
        self.file_upload_service.delete_file(self.MOCK_UPLOADED_FILE_ID)

        self.drive_client.delete_file.assert_called_with(self.MOCK_UPLOADED_FILE_ID)

    @patch.object(FileUploadService, '_FileUploadService__create_common_folder')
    def test_common_file_upload_with_existing_folder(self, mock_create_common_folder):
        mock_folder = self.get_mock_common_folder()
        self.drive_repository.get_folder_by_name.side_effect = lambda \
                name: mock_folder if name == self.COMMON_FOLDER_NAME else None

        mock_file_model = self.get_mock_common_file_model()
        mock_file_data = self.get_mock_file_data()
        mock_uploaded_file = self.get_mock_uploaded_file()
        self.drive_client.upload_file.side_effect = \
            lambda stream, mime_type, folder_id, file_name: mock_uploaded_file \
                if (stream == mock_file_data.stream) \
                   and (mime_type == mock_file_data.mimetype) \
                   and (folder_id == mock_folder.folder_id) \
                   and (file_name == mock_file_model.file_name) else None

        uploaded_file = self.file_upload_service._FileUploadService__upload_common_file(mock_file_model, mock_file_data)

        self.assertFalse(mock_create_common_folder.called)
        self.assertEqual(uploaded_file, mock_uploaded_file)

    @patch.object(FileUploadService, '_FileUploadService__create_common_folder')
    def test_common_file_upload_with_new_folder(self, mock_create_common_folder):
        mock_folder = self.get_mock_common_folder()
        self.drive_repository.get_folder_by_name.side_effect = lambda \
                name: None if name == self.COMMON_FOLDER_NAME else mock_folder
        mock_create_common_folder.return_value = mock_folder

        mock_file_model = self.get_mock_common_file_model()
        mock_file_data = self.get_mock_file_data()
        mock_uploaded_file = self.get_mock_uploaded_file()
        self.drive_client.upload_file.side_effect = \
            lambda stream, mime_type, folder_id, file_name: mock_uploaded_file \
                if (stream == mock_file_data.stream) \
                   and (mime_type == mock_file_data.mimetype) \
                   and (folder_id == mock_folder.folder_id) \
                   and (file_name == mock_file_model.file_name) \
                else None

        uploaded_file = self.file_upload_service._FileUploadService__upload_common_file(mock_file_model, mock_file_data)

        self.assertTrue(mock_create_common_folder.called)
        self.assertEqual(uploaded_file, mock_uploaded_file)

    @patch('cataloging_service.domain.file_upload_service.app')
    def test_common_folder_creation(self, app):
        app.config = self.CONFIG
        self.drive_client.create_folder.return_value = self.get_mock_uploaded_common_folder()
        mock_common_folder = self.get_mock_common_folder()

        common_folder = self.file_upload_service._FileUploadService__create_common_folder()
        self.assertEqual(common_folder.folder_name, mock_common_folder.folder_name)
        self.assertEqual(common_folder.folder_id, mock_common_folder.folder_id)
        self.assertEqual(common_folder.folder_link, mock_common_folder.folder_link)

    @patch.object(FileUploadService, '_FileUploadService__create_property_folder')
    def test_upload_property_file_with_new_folder(self, mock_create_folder):
        mock_folder = self.get_mock_property_folder()
        self.drive_repository.get_property_folder.side_effect = lambda \
                property_id: None if property_id == self.MOCK_PROPERTY_ID else mock_folder

        mock_file_model = self.get_mock_property_file_model()
        mock_file_data = self.get_mock_file_data()

        mock_create_folder.side_effect = \
            lambda file_model, file_data: mock_folder \
                if (file_model == mock_file_model) \
                   and (file_data == mock_file_data) \
                else None
        mock_uploaded_file = self.get_mock_uploaded_file()
        self.drive_client.upload_file.return_value = mock_uploaded_file

        uploaded_file = self.file_upload_service._FileUploadService__upload_property_file(mock_file_model,
                                                                                          mock_file_data)

        self.assertEqual(uploaded_file, mock_uploaded_file)
        self.assertTrue(mock_create_folder.called)

    @patch.object(FileUploadService, '_FileUploadService__create_property_folder')
    def test_upload_property_file_with_existing_folder(self, mock_create_folder):
        mock_folder = self.get_mock_property_folder()
        self.drive_repository.get_property_folder.side_effect = lambda \
                property_id: mock_folder if property_id == self.MOCK_PROPERTY_ID else None

        mock_file_model = self.get_mock_property_file_model()
        mock_file_data = self.get_mock_file_data()

        mock_create_folder.side_effect = \
            lambda file_model, file_data: mock_folder \
                if (file_model == mock_file_model) \
                   and (file_data == mock_file_data) \
                else None
        mock_uploaded_file = self.get_mock_uploaded_file()
        self.drive_client.upload_file.return_value = mock_uploaded_file

        uploaded_file = self.file_upload_service._FileUploadService__upload_property_file(mock_file_model,
                                                                                          mock_file_data)

        self.assertEqual(uploaded_file, mock_uploaded_file)
        self.assertFalse(mock_create_folder.called)

    @patch.object(FileUploadService, '_FileUploadService__create_property_folder')
    def test_upload_property_document_file_with_existing_folder(self, mock_create_folder):
        mock_folder = self.get_mock_property_folder()
        self.drive_repository.get_property_folder.side_effect = lambda \
                property_id: mock_folder if property_id == self.MOCK_PROPERTY_ID else None

        mock_file_model = self.get_mock_property_file_model()
        mock_file_data = self.get_mock_file_data()

        mock_create_folder.side_effect = \
            lambda file_model, file_data: mock_folder \
                if (file_model == mock_file_model) \
                   and (file_data == mock_file_data) \
                else None
        mock_uploaded_file = self.get_mock_uploaded_file()
        self.drive_client.upload_file.return_value = mock_uploaded_file

        mock_file_data.mimetype = 'image/png'
        uploaded_file = self.file_upload_service._FileUploadService__upload_property_file(mock_file_model,
                                                                                          mock_file_data)

        self.assertEqual(uploaded_file, mock_uploaded_file)
        self.assertFalse(mock_create_folder.called)

    @patch('cataloging_service.domain.file_upload_service.app')
    def test_property_folder_creation(self, app):
        app.config = self.CONFIG

        self.drive_client.create_folder.side_effect = self.create_property_folder_side_effect
        mock_property_folder = self.get_mock_property_folder()

        mock_file_model = self.get_mock_property_file_model()
        mock_file_data = self.get_mock_file_data()

        property_folder = self.file_upload_service._FileUploadService__create_property_folder(mock_file_model,
                                                                                              mock_file_data)
        self.assertEqual(property_folder.folder_name, mock_property_folder.folder_name)
        self.assertEqual(property_folder.folder_id, mock_property_folder.folder_id)
        self.assertEqual(property_folder.folder_link, mock_property_folder.folder_link)
        self.assertEqual(property_folder.property_documents_file_id, mock_property_folder.property_documents_file_id)
        self.assertEqual(property_folder.property_documents_link, mock_property_folder.property_documents_link)
        self.assertEqual(property_folder.property_images_file_id, mock_property_folder.property_images_file_id)
        self.assertEqual(property_folder.property_images_link, mock_property_folder.property_images_link)

    def create_property_folder_side_effect(self, folder_id, folder_name):
        mock_base_folder = self.get_mock_uploaded_property_folder()
        mock_document_folder = self.get_mock_uploaded_document_folder()
        mock_image_folder = self.get_mock_uploaded_image_folder()

        if folder_name == self.MOCK_DOCUMENT_FOLDER_NAME:
            return mock_document_folder
        elif folder_name == self.MOCK_IMAGE_FOLDER_NAME:
            return mock_image_folder
        return mock_base_folder

    def get_mock_common_file_model(self):
        mock_file_model = Mock(spec=GoogleDriveFile)
        mock_file_model.property = None
        mock_file_model.file_id = None
        mock_file_model.file_name = self.MOCK_FILE_NAME
        return mock_file_model

    def get_mock_property_file_model(self):
        mock_file_model = Mock(spec=GoogleDriveFile)
        mock_file_model.property = Mock(spec=Property)
        mock_file_model.property.id = self.MOCK_PROPERTY_ID
        mock_file_model.file_id = None
        mock_file_model.file_name = self.MOCK_FILE_NAME
        return mock_file_model

    def get_mock_common_folder(self):
        mock_folder = Mock(spec=GoogleDriveBaseFolder)
        mock_folder.folder_name = self.COMMON_FOLDER_NAME
        mock_folder.folder_id = self.MOCK_FOLDER_ID
        mock_folder.folder_link = self.MOCK_FOLDER_LINK
        return mock_folder

    def get_mock_property_folder(self):
        mock_folder = Mock(spec=GoogleDriveBaseFolder)
        mock_folder.folder_name = self.MOCK_PROPERTY_FOLDER_NAME
        mock_folder.folder_id = self.MOCK_FOLDER_ID
        mock_folder.folder_link = self.MOCK_FOLDER_LINK
        mock_folder.property_documents_file_id = self.MOCK_FOLDER_ID
        mock_folder.property_documents_link = self.MOCK_FOLDER_LINK
        mock_folder.property_images_file_id = self.MOCK_FOLDER_ID
        mock_folder.property_images_link = self.MOCK_FOLDER_LINK
        return mock_folder

    def get_mock_file_data(self):
        mock_object = Mock()
        mock_object.stream = 'stream'
        mock_object.mimetype = 'mime'
        return mock_object

    def get_mock_uploaded_file(self):
        uploaded_file = Mock(spec=GoogleDriveFileResource)
        uploaded_file.file_id = self.MOCK_UPLOADED_FILE_ID
        return uploaded_file

    def get_mock_uploaded_common_folder(self):
        uploaded_folder = Mock(spec=GoogleDriveFileResource)
        uploaded_folder.file_id = self.MOCK_FOLDER_ID
        uploaded_folder.name = self.COMMON_FOLDER_NAME
        uploaded_folder.link = self.MOCK_FOLDER_LINK
        return uploaded_folder

    def get_mock_uploaded_property_folder(self):
        uploaded_folder = Mock(spec=GoogleDriveFileResource)
        uploaded_folder.file_id = self.MOCK_FOLDER_ID
        uploaded_folder.name = self.MOCK_PROPERTY_FOLDER_NAME
        uploaded_folder.link = self.MOCK_FOLDER_LINK
        return uploaded_folder

    def get_mock_uploaded_document_folder(self):
        uploaded_folder = Mock(spec=GoogleDriveFileResource)
        uploaded_folder.file_id = self.MOCK_FOLDER_ID
        uploaded_folder.name = self.MOCK_DOCUMENT_FOLDER_NAME
        uploaded_folder.link = self.MOCK_FOLDER_LINK
        return uploaded_folder

    def get_mock_uploaded_image_folder(self):
        uploaded_folder = Mock(spec=GoogleDriveFileResource)
        uploaded_folder.file_id = self.MOCK_FOLDER_ID
        uploaded_folder.name = self.MOCK_IMAGE_FOLDER_NAME
        uploaded_folder.link = self.MOCK_FOLDER_LINK
        return uploaded_folder
