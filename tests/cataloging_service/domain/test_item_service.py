from datetime import datetime, timed<PERSON><PERSON>
from unittest import mock
from unittest.mock import patch, Mock

from cataloging_service.api.request_objects import ItemRequest, VariantGroupRequest
from cataloging_service.constants import error_codes, model_choices, constants
from cataloging_service.domain.item_service import ItemService
from cataloging_service.exceptions import CatalogingServiceException
from cataloging_service.infrastructure import decorators

from tests.cataloging_service.cataloging_service_test_case import CatalogingServiceTestCase


class TestItemService(CatalogingServiceTestCase):
    def setUp(self):

        self.item_repository = Mock()
        self.item_repository.persist.side_effect = lambda x: x
        self.item_service = ItemService(item_repository=self.item_repository)
    
    def tearDown(self):
        pass

    def get_item_request(self):
        dictionary = {
            "name": "Cake",
            "code": "Cake",
            "description": "Cake",
            "sku_category_id": 12,
            "display_name": "Cake",
            "print_name": "Cake",
            "prep_time": 15,
            "use_as_side": False,
            "contains_alcohol": False,
            "pre_tax_price": 10.0,
            "cost": 11.00,
            "sku_id": 345
        }

        return ItemRequest(seller_id='1', dictionary=dictionary)

    
    def get_item_with_none_sku_id_request(self):
        dictionary = {
            "name": "Cake",
            "code": "Cake",
            "description": "Cake",
            "sku_category_id": 12,
            "display_name": "Cake",
            "print_name": "Cake",
            "prep_time": 15,
            "use_as_side": False,
            "contains_alcohol": False,
            "pre_tax_price": 10.0,
            "cost": 11.00,
            "sku_id": None
        }

        return ItemRequest(seller_id='1', dictionary=dictionary)

    def get_variant_group_request(self):
        dictionary = {
            "name": "Colour",
            "display_name": "Colour",
            "can_select_multiple": False,
            "can_select_quantity": True,
            "minimum_selectable_quantity": None,
            "maximum_selectable_quantity": None,
            "is_customisation": False,
            "variants": [
                {
                    "name": "Blue",
                    "display_order": 1
                },
                {
                    "name": "Red",
                    "display_order": 2
                }
            ]
        }
        return VariantGroupRequest("1", dictionary=dictionary)

    def test_create_item(self):
        item_request = self.get_item_request()
        item_object = self.item_service.create_item(item_request)

        self.assertEqual(item_object.seller_id, item_request.seller_id)
        self.assertEqual(item_object.name, item_request.name)
        self.assertEqual(item_object.description, item_request.description)
        self.assertEqual(item_object.sku_category_id, item_request.sku_category_id)
        self.assertEqual(item_object.display_name, item_request.display_name)
        self.assertEqual(item_object.print_name, item_request.print_name)
        self.assertEqual(item_object.prep_time, item_request.prep_time)
        self.assertEqual(item_object.use_as_side, item_request.use_as_side)
        self.assertEqual(item_object.contains_alcohol, item_request.contains_alcohol)
        self.assertEqual(item_object.pre_tax_price, item_request.pre_tax_price)
        self.assertEqual(item_object.cost, item_request.cost)
        self.assertEqual(item_object.sku_id, item_request.sku_id)

    def test_create_item_with_none_sku_id(self):
        item_request = self.get_item_with_none_sku_id_request()
        item_object = self.item_service.create_item(item_request)

        self.assertEqual(item_object.sku_id, None)
        self.assertEqual(item_object.name, item_request.name)
        self.assertEqual(item_object.description, item_request.description)
        self.assertEqual(item_object.sku_category_id, item_request.sku_category_id)
        self.assertEqual(item_object.display_name, item_request.display_name)
        self.assertEqual(item_object.print_name, item_request.print_name)
        self.assertEqual(item_object.prep_time, item_request.prep_time)
        self.assertEqual(item_object.use_as_side, item_request.use_as_side)
        self.assertEqual(item_object.contains_alcohol, item_request.contains_alcohol)
        self.assertEqual(item_object.pre_tax_price, item_request.pre_tax_price)
        self.assertEqual(item_object.cost, item_request.cost)
        self.assertEqual(item_object.sku_id, item_request.sku_id)
        self.assertEqual(item_object.seller_id, item_request.seller_id)

    def test_create_variant_group(self):
        variant_group_request = self.get_variant_group_request()
        variant_group_object, variant_list = self.item_service.create_variant_group(variant_group_request)
        
        self.assertEqual(variant_group_object.seller_id, variant_group_request.seller_id)
        self.assertEqual(variant_group_object.display_name, variant_group_request.display_name)
        self.assertEqual(variant_group_object.can_select_multiple, variant_group_request.can_select_multiple)
        self.assertEqual(variant_group_object.can_select_quantity, variant_group_request.can_select_quantity)
        self.assertEqual(variant_group_object.minimum_selectable_quantity, variant_group_request.minimum_selectable_quantity)
        self.assertEqual(variant_group_object.is_customisation, variant_group_request.is_customisation)
