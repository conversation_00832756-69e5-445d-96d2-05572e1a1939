from unittest.mock import Mock

from cataloging_service.domain.messaging_service import MessagingService
from cataloging_service.infrastructure.messaging.messages import Messages
from cataloging_service.models import Property, Room, RoomTypeConfiguration, Restaurant, Bar, BanquetHall, \
    PropertyAmenity, RoomAmenity, SkuCategory, Sku, PropertyImage, AmenitySummary, Channel, SubChannel, Application, \
    PricingPolicy
from tests.cataloging_service.cataloging_service_test_case import CatalogingServiceTestCase


class TestMessagingService(CatalogingServiceTestCase):
    def setUp(self):
        self.mock_publisher = Mock()
        self.messaging_service = MessagingService(self.mock_publisher)

    def test_publish_all_messages(self):
        messages = Messages()
        messages.properties = ['Property1', 'Property 2']
        messages.rooms = ['Room1', 'Room2']
        messages.room_configs = ['Config1', 'Config2']
        messages.restaurants = ['Restaurant1', 'Restaurant2']
        messages.bars = ['Bar1', 'Bar2']
        messages.halls = ['Hall1', 'Hall2']
        messages.property_amenities = ['PropertyAmenity1', 'PropertyAmenity2']
        messages.room_amenities = ['RoomAmenity1', 'RoomAmenity2']
        messages.property_images = ['Image1', 'Image2']
        messages.amenity_summaries = ['summary1', 'summary2']
        messages.sku_categories = ['sku1Cat']
        messages.skus = ['sku1']
        messages.channels = ['channel1', 'channel2']
        messages.sub_channels = ['subchannel1', 'subchannel2']
        messages.applications = ['application1']
        messages.cities = ['Bangalore', 'Bhubaneswar']
        messages.pricing_policies = ['P1', 'P2']

        self.messaging_service.publish_all_messages(messages)

        self.assertEqual(self.mock_publisher.publish.call_count, 31)

    def test_create_messages(self):
        new_objects = {self.get_mock_property(), self.get_mock_room(), self.get_mock_room_config(),
                       self.get_mock_restaurant(), self.get_mock_banquet_hall(), self.get_mock_bar(),
                       self.get_mock_property_amenity(), self.get_mock_room_amenity(), self.get_mock_sku(),
                       PropertyImage(id=1, property=self.get_mock_property()),
                       AmenitySummary(id=1, property=self.get_mock_property()), self.get_mock_sku_category(),
                       Channel(id='ch1'), SubChannel(id='subch2'), Application(id='app1')
                       }
        modified_objects = {self.get_mock_property(), self.get_mock_room(), self.get_mock_room_config(),
                            self.get_mock_restaurant(), self.get_mock_banquet_hall(), self.get_mock_bar(),
                            self.get_mock_property_amenity(), self.get_mock_room_amenity(), self.get_mock_sku_category(),
                            PropertyImage(id=1, property=self.get_mock_property()),
                            AmenitySummary(id=1, property=self.get_mock_property()), self.get_mock_sku(),
                            Channel(id='ch1'), SubChannel(id='subch2'), Application(id='app1'), PricingPolicy(id=1)
                            }
        deleted_objects = {self.get_mock_property(), self.get_mock_room(), self.get_mock_room_config(),
                           self.get_mock_restaurant(), self.get_mock_banquet_hall(), self.get_mock_bar(),
                           self.get_mock_property_amenity(), self.get_mock_room_amenity(),
                           PropertyImage(id=1, property=self.get_mock_property()), self.get_mock_sku_category(),
                           AmenitySummary(id=1, property=self.get_mock_property()), self.get_mock_sku(),
                           Channel(id='ch1'), SubChannel(id='subch2'), Application(id='app1')
                           }

        messages = Messages()
        messages = self.messaging_service.create_publishable_messages(new_objects, modified_objects, deleted_objects,
                                                                      messages)

        self.assertEqual(len(messages.properties), 3)
        self.assertEqual(len(messages.rooms), 3)
        self.assertEqual(len(messages.room_configs), 3)
        self.assertEqual(len(messages.restaurants), 3)
        self.assertEqual(len(messages.bars), 3)
        self.assertEqual(len(messages.halls), 3)
        self.assertEqual(len(messages.property_amenities), 3)
        self.assertEqual(len(messages.room_amenities), 3)
        self.assertEqual(len(messages.skus), 3)
        self.assertEqual(len(messages.property_images), 3)
        self.assertEqual(len(messages.amenity_summaries), 1)
        self.assertEqual(len(messages.sku_categories), 2)
        self.assertEqual(len(messages.skus), 3)
        self.assertEqual(len(messages.channels), 3)
        self.assertEqual(len(messages.sub_channels), 3)
        self.assertEqual(len(messages.applications), 3)
        self.assertEqual(len(messages.pricing_policies), 1)

    def get_mock_property(self):
        property_object = Property()
        property_object.id = 1
        property_object.hx_id = 'HXID'

        return property_object

    def get_mock_room(self):
        room_object = Room()
        room_object.property = self.get_mock_property()
        room_object.id = 1

        return room_object

    def get_mock_room_config(self):
        config = RoomTypeConfiguration()
        config.id = 1
        config.property = self.get_mock_property()

        return config

    def get_mock_restaurant(self):
        restaurant = Restaurant()
        restaurant.id = 1
        restaurant.property = self.get_mock_property()

        return restaurant

    def get_mock_bar(self):
        bar = Bar()
        bar.id = 1
        bar.property = self.get_mock_property()

        return bar

    def get_mock_banquet_hall(self):
        banquet_hall = BanquetHall()
        banquet_hall.id = 1
        banquet_hall.property = self.get_mock_property()

        return banquet_hall

    def get_mock_property_amenity(self):
        property_amenity = PropertyAmenity()
        property_amenity.id = 1
        property_amenity.property = self.get_mock_property()

        return property_amenity

    def get_mock_room_amenity(self):
        room_amenity = RoomAmenity()
        room_amenity.id = 1
        room_amenity.room = self.get_mock_room()

        return room_amenity

    def get_mock_sku(self):
        sku = Sku()
        sku.id = 1
        sku.name = 'OAK1'
        sku.hsn_sac = 'stay_hsn'
        sku.code = 'oak1'
        sku.saleable = False
        sku.chargeable_per_occupant = False
        sku.is_modular = True
        sku.category_id = self.get_mock_sku_category()
        return sku

    def get_mock_sku_category(self):
        sku_category = SkuCategory()
        sku_category.id = 1
        sku_category.name = 'Stay'
        sku_category.status = 'ACTIVE'
        sku_category.hsn_sac = 'stay_hsn'
        return sku_category
